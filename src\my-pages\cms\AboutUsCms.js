import React, { useState, useEffect } from "react";
import axios from "axios";
import {
  Box,
  Button,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Card,
  CardBody,
  Divider,
  Flex,
  FormControl,
  Heading,
  Input,
  Spinner,
  Text,
  useToast,
  Image,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import * as Yup from "yup";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import Layout from "../../layout/default";
import { useSelector } from "react-redux";

const AboutUsCms = () => {
  const [aboutUsData, setAboutUsData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [isUpdated, setIsUpdated] = useState(false);
  const [discardBtnLoading, setDiscardBtnLoading] = useState(false);
  const [saveBtnLoading, setSaveBtnLoading] = useState(false);
  const Toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  // Fetch about us data
  const getAboutUs = () => {
    setAboutUsData({ result: [], isLoading: true, error: false });
    axios
      .get(`${process.env.REACT_APP_BASE_URL}/api/cms/cms-about-us`)
      .then((response) => {
        setAboutUsData({
          result: response.data,
          isLoading: false,
          error: false,
        });
        setDiscardBtnLoading(false);
        setIsUpdated(false);
      })
      .catch((error) => {
        setAboutUsData({ result: [], isLoading: false, error: true });
        setDiscardBtnLoading(false);
        handleErrorToast(error);
      });
  };

  const handleErrorToast = (error) => {
    const errorMessage =
      error.response?.status === 403
        ? "You don't have access to perform this action"
        : "Something went wrong, please try again later";
    Toast({
      title: errorMessage,
      status: error.response?.status === 403 ? "warning" : "error",
      duration: 4000,
      position: "top",
      isClosable: true,
    });
  };

  // Formik for form handling
  const formik = useFormik({
    initialValues: {
      aboutUsData: aboutUsData.result[0]?.aboutUsData || "",
      founderDetails: aboutUsData.result[0]?.founderDetails || [
        { description: "", image: "" },
      ],
    },
    enableReinitialize: true,
    validationSchema: Yup.object({
      aboutUsData: Yup.string().min(10, "Please add at least 10 words"),
      founderDetails: Yup.array().of(
        Yup.object({
          description: Yup.string(),
          image: Yup.string(),
        })
      ),
    }),
    onSubmit: (values) => {
      const payload = {
        aboutUsData: values.aboutUsData,
        founderDetails: values.founderDetails,
      };

      axios
        .patch(
          `${process.env.REACT_APP_BASE_URL}/api/cms/update/cms-about-us/${aboutUsData.result[0]._id}`,
          payload,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        )
        .then(() => {
          Toast({
            title: "About Us updated successfully!",
            status: "success",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
          setIsUpdated(false);
        })
        .catch((error) => handleErrorToast(error));
    },
  });

  const addFounderDetail = () => {
    formik.setFieldValue("founderDetails", [
      ...formik.values.founderDetails,
      { description: "", image: "" },
    ]);
  };

  const handleFileChange = async (e, index) => {
    try {
      const file = e.currentTarget.files[0];
      if (!file) return;

      // Check file size
      if (file.size > 10 * 1024 * 1024) {
        Toast({
          title: "Please select a file less than 10 MB",
          status: "warning",
          duration: 5000,
          position: "top",
          isClosable: true,
        });
        e.target.value = null; // Clear input
        return;
      }

      const formData = new FormData();
      formData.append("image", file);

      // Upload image
      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/cms/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const url = response?.data?.url;

      if (url) {
        // Success toast
        Toast({
          title: "Image uploaded successfully",
          status: "success",
          duration: 3000,
          position: "top",
          isClosable: true,
        });

        // Update formik values
        const newFounderDetails = [...formik.values.founderDetails];
        newFounderDetails[index].image = url; // Set the image URL
        await formik.setFieldValue("founderDetails", newFounderDetails);

        e.target.value = null; // Clear input
      } else {
        throw new Error("Invalid response URL");
      }
    } catch (error) {
      e.target.value = null; // Clear input

      // Handle errors
      if (error.response?.status === 403) {
        Toast({
          title: "You don't have access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        Toast({
          title: "Something went wrong. Please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const deleteImageFiles = async (url, index) => {
    if (!userData?.accessScopes?.coach?.includes("delete")) {
      Toast({
        title: "You don't have an access to perform this action",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else {
      try {
        const formData = new FormData();
        formData.append("url", url);

        const response = await axios.post(
          `${process.env.REACT_APP_BASE_URL}/api/cms/upload`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        const newFounderDetails = [...formik.values.founderDetails];
        newFounderDetails[index].image = ""; // Or process file URL here
        await formik.setFieldValue("founderDetails", newFounderDetails);

        const resp = response?.data;
        if (resp) {
          if (url) {
            Toast({
              title: "Profile image deleted.",
              status: "success",
              duration: 3000,
              position: "top",
              isClosable: true,
            });
          } else {
            Toast({
              title: "Something went wrong while deleting profile image.",
              status: "error",
              duration: 3000,
              position: "top",
              isClosable: true,
            });
          }
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 403) {
          Toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          Toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      }
    }
  };

  useEffect(() => {
    getAboutUs();
  }, []);

  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="CMS | About Us" content="container">
        <Flex justifyContent="space-between" alignItems="center">
          <Breadcrumb fontWeight="medium" fontSize="sm">
            <BreadcrumbItem>
              <BreadcrumbLink>Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink>CMS</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink>About Us</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
          <Box>
            <Button
              variant="outline"
              colorScheme="red"
              size="sm"
              isDisabled={!isUpdated}
              isLoading={discardBtnLoading}
              onClick={getAboutUs}
            >
              Discard
            </Button>
            <Button
              variant="outline"
              colorScheme="green"
              size="sm"
              isDisabled={!isUpdated}
              isLoading={saveBtnLoading}
              onClick={formik.handleSubmit}
            >
              Save Changes
            </Button>
          </Box>
        </Flex>

        {/* Main Content */}
        {aboutUsData.isLoading ? (
          <Flex justifyContent="center" mt={12}>
            <Spinner size="lg" />
          </Flex>
        ) : !aboutUsData.error ? (
          <Card mt={4}>
            <CardBody>
              <Heading size="md" mb={3}>
                About Us
              </Heading>
              <ReactQuill
                theme="snow"
                value={formik.values.aboutUsData}
                onChange={(value) => {
                  formik.setFieldValue("aboutUsData", value);
                  setIsUpdated(true);
                }}
              />
              <Divider my={6} />
              <Heading size="md" mb={3}>
                Founders Detail
              </Heading>

              {formik.values.founderDetails.map((founder, index) => (
                <Box key={index} mb={4}>
                  <Flex
                    justifyContent={"space-between"}
                    alignItems={"center"}
                    mb={4}
                  >
                    <Heading size="md" mb={3}>
                      Details {index + 1}
                    </Heading>
                    {index !== 0 &&
                      userData?.accessScopes?.coach?.includes("delete") && (
                        <Button
                          colorScheme="red"
                          size={"sm"}
                          px={4}
                          onClick={() =>
                            formik.setValues((prevState) => ({
                              ...prevState,
                              founderDetails: prevState.founderDetails.filter(
                                (_, i) => i !== index
                              ),
                            }))
                          }
                        >
                          Remove
                        </Button>
                      )}
                  </Flex>
                  <ReactQuill
                    theme="snow"
                    value={founder.description}
                    onChange={(value) => {
                      const newFounderDetails = [
                        ...formik.values.founderDetails,
                      ];
                      newFounderDetails[index].description = value;
                      formik.setFieldValue("founderDetails", newFounderDetails);
                      setIsUpdated(true);
                    }}
                  />
                  {founder.image ? (
                    <>
                      <a
                        href={`${formik?.values?.founderDetails[index]?.image}`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Image
                          src={formik?.values?.founderDetails[index]?.image}
                          id={`founderDetails.${index}.image`}
                          alt="Selected Image"
                          maxW="165px"
                          maxH="165px"
                        />
                      </a>
                      {userData?.accessScopes?.coach?.includes("delete") && (
                        <Button
                          mt={2}
                          colorScheme="red"
                          size={"sm"}
                          onClick={(e) => {
                            e.preventDefault();
                            formik.setFieldValue(
                              `founderDetails.${index}.image`,
                              ""
                            );
                            // setCoachingExperienceImages(
                            //   coachingExperienceImages.filter(
                            //     (x, idx) => idx !== index + 1
                            //   )
                            // );
                            deleteImageFiles(founder.image, index);
                          }}
                        >
                          Remove Image
                        </Button>
                      )}
                    </>
                  ) : (
                    <Input
                      type="file"
                      accept="image/*"
                      mt={2}
                      onChange={(e) => handleFileChange(e, index)}
                    />
                  )}
                </Box>
              ))}
              <Button mt={4} colorScheme="green" onClick={addFounderDetail}>
                Add Founder
              </Button>
            </CardBody>
          </Card>
        ) : (
          <Text>Error loading About Us data.</Text>
        )}
      </Layout>
    </Box>
  );
};

export default AboutUsCms;
