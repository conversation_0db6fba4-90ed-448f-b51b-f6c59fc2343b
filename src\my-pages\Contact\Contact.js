import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import { <PERSON>d<PERSON><PERSON><PERSON>, MdD<PERSON><PERSON><PERSON><PERSON> } from "react-icons/md";
import { IoIosClose, IoMdSearch } from "react-icons/io";
import { IoFilter } from "react-icons/io5";
import {
  Box,
  BreadcrumbLink,
  Input,
  Breadcrumb,
  BreadcrumbItem,
  Card,
  CardBody,
  Button,
  Stack,
  Text,
  Flex,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  TableContainer,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  useToast,
  Spinner,
  Tooltip,
  Badge,
  Select,
  InputGroup,
  InputRightAddon,
} from "@chakra-ui/react";
import axios from "axios";
import { useSelector } from "react-redux";
import ReactPaginate from "react-paginate";
import { Link, useNavigate } from "react-router-dom";

const Contact = () => {
  const [contactData, setContactData] = useState({
    result: [],
    isLoading: false,
    error: false,
    notFound: false,
  });

  const [coachSearchDetails, setCoachSearchDetails] = useState({
    result: [],
    isLoading: false,
    error: false,
  });

  const [searchQueryCoach, setSearchQueryCoach] = useState("");
  const [searchCourseName, setSearchCourseName] = useState("");
  const [selectedClassType, setSelectedClassType] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");

  const [isSearched, setIsSearched] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [showSearch, setShowSearch] = useState(false);

  const [isOpen2, setIsOpen2] = useState(false);
  const [isOpen3, setIsOpen3] = useState(false);
  const [selectedContact, setSelectedContact] = useState(null);
  const [newStatus, setNewStatus] = useState("");
  const onClose2 = () => setIsOpen2(false);
  const onClose3 = () => setIsOpen3(false);
  const onOpen2 = () => setIsOpen2(true);
  const onOpen3 = () => setIsOpen3(true);

  const toast = useToast();
  const navigate = useNavigate();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getCourseData = (searchCourseName, classType, status) => {
    setContactData({
      result: [],
      isLoading: true,
      error: false,
      notFound: false,
    });

    let queryString = `?page=${currentPage}`;
    if (searchCourseName) {
      queryString += `&courseName=${searchCourseName}`;
    }
    if (status) {
      queryString += `&status=${status}`;
    }
    if (classType) {
      queryString += `&userType=${classType}`;
    }
    let config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_URL}/api/contactUs${queryString}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setContactData({
          result: response.data.data,
          isLoading: false,
          error: false,
          notFound: response.data.data.length === 0,
        });
        setTotalPages(Math.ceil(response.data.totalResults / 25));
      })
      .catch((error) => {
        console.log(error);
        setContactData({
          result: [],
          isLoading: false,
          error: true,
          notFound: false,
        });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const getCoaches = async (name, page) => {
    let url = "";
    if (name) {
      url += `${process.env.REACT_APP_BASE_URL}/api/coach?firstName=${name}`;
    } else {
      url += `${process.env.REACT_APP_BASE_URL}/api/coach?page=${page}`;
    }

    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: url,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setCoachSearchDetails({
          result: response.data.data,
          isLoading: false,
          error: false,
        });
        if (response.data.data.length === 0) {
          setIsSearched(true);
        }
      })
      .catch((error) => {
        console.log(error);
        setCoachSearchDetails({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handlePageChange = ({ selected }) => {
    setCurrentPage(selected + 1);
  };

  const handleStatusChange = (contact, newStatus) => {
    setSelectedContact(contact);
    setNewStatus(newStatus);
    onOpen3();
  };

  const confirmStatusChange = () => {
    if (!selectedContact || !newStatus) return;

    let config = {
      method: "patch",
      url: `${process.env.REACT_APP_BASE_URL}/api/contactUs/${selectedContact._id}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
      data: {
        status: newStatus,
      },
    };

    axios
      .request(config)
      .then(() => {
        toast({
          title: "Status updated successfully",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        getCourseData(searchCourseName, selectedClassType, selectedStatus);
      })
      .catch((error) => {
        console.log(error);
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .finally(() => {
        onClose3();
      });
  };

  useEffect(() => {
    getCourseData(searchCourseName, selectedClassType, selectedStatus);
  }, [currentPage, searchCourseName, selectedClassType, selectedStatus]);

  useEffect(() => {
    getCoaches("", 1);
  }, []);

  return (
    <Layout title="Contact" content="container">
      <Flex
        w={"100%"}
        justifyContent={"space-between"}
        alignItems={"center"}
        mb={6}
      >
        <Flex
          flexBasis={"59%"}
          justifyContent={"space-between"}
          alignItems={"center"}
        >
          <Breadcrumb fontWeight="medium" fontSize="sm">
            <BreadcrumbItem>
              <Link to={"/"}>Dashboard</Link>
            </BreadcrumbItem>
            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink href="#">Contact</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
        </Flex>
        <Flex flexBasis={"40%"} justifyContent={"space-evenly"}>
          <Box flexBasis={"33%"}>
            <Select
              placeholder="User Type"
              borderColor={"gray.300"}
              cursor={"pointer"}
              bgColor={selectedClassType && "gray.300"}
              onChange={(e) => {
                setSelectedClassType(e.target.value);
                setCurrentPage(1);
              }}
            >
              <option value="">All</option>
              <option value="coach">Coach</option>
              <option value="player">Player</option>
            </Select>
          </Box>
          <Box flexBasis={"31%"}>
            <Select
              placeholder="Status"
              borderColor={"gray.300"}
              cursor={"pointer"}
              bgColor={selectedStatus && "gray.300"}
              onChange={(e) => {
                setSelectedStatus(e.target.value);
                setCurrentPage(1);
              }}
            >
              <option value="">All</option>
              <option value="active">Active</option>
              <option value="inactive">In-Active</option>
            </Select>
          </Box>
        </Flex>
      </Flex>

      {/* Added/Selected Course List */}
      {!contactData?.isLoading && contactData?.error ? (
        <Flex
          justifyContent={"center"}
          alignItems={"center"}
          w={"full"}
          my={10}
        >
          <Text color={"red.500"}>
            Something went wrong please try again later...
          </Text>
        </Flex>
      ) : (
        <TableContainer
          mt={6}
          height={`${window.innerHeight - 235}px`}
          overflowY={"scroll"}
        >
          <Table variant="simple">
            <Thead
              bgColor={"#c1eaee"}
              position={"sticky"}
              top={"0px"}
              zIndex={"99"}
            >
              <Tr bgColor={"#E2DFDF"}>
                <Th>Name</Th>
                <Th>Email</Th>
                <Th>Mobile</Th>
                <Th>User Type</Th>
                <Th>Message</Th>
                <Th>Status</Th>
              </Tr>
            </Thead>
            <Tbody>
              {contactData?.isLoading && !contactData?.error ? (
                <Tr>
                  <Td></Td>
                  <Td> </Td>
                  <Td
                    display={"flex"}
                    justifyContent={"flex-end"}
                    alignItems={"center"}
                  >
                    <Spinner />
                  </Td>
                  <Td></Td>
                  <Td></Td>
                </Tr>
              ) : !contactData?.notFound ? (
                contactData?.result?.map((contact, inx) => (
                  <Tr key={inx}>
                    <Td>{contact?.firstName} {contact?.lastName}</Td>
                    <Td>{contact?.email}</Td>
                    <Td>{contact?.mobile}</Td>
                    <Td>{contact?.userType}</Td>
                    
                    <Td>
                      <Button
                        size={"sm"}
                        bgColor={"gray.300"}
                        onClick={() => {
                          setSelectedContact(contact);
                          onOpen2();
                        }}
                      >
                        View
                      </Button>
                    </Td>
                    <Td>
                      <Select
                        defaultValue={contact?.status}
                        onChange={(e) =>
                          handleStatusChange(contact, e.target.value)
                        }
                        bgColor={
                          contact?.status === "active" ? "green.100" : "red.100"
                        }
                        isDisabled={contact?.status === 'inactive'}
                      >
                        <option value="active" style={{ color: "green" }}>
                          Active
                        </option>
                        <option value="inactive" style={{ color: "red" }}>
                          In-Active
                        </option>
                      </Select>
                    </Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td></Td>
                  <Td> </Td>
                  <Td>No Contact Found</Td>
                  <Td></Td>
                  <Td></Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </TableContainer>
      )}
      {contactData.result.length > 0 && (
        <Flex justifyContent="flex-end" mt={4}>
          <ReactPaginate
            previousLabel={"Previous"}
            nextLabel={"Next"}
            breakLabel={"..."}
            breakClassName={"break-me"}
            pageCount={totalPages}
            marginPagesDisplayed={2}
            pageRangeDisplayed={5}
            onPageChange={handlePageChange}
            containerClassName={"pagination"}
            activeClassName={"active"}
            forcePage={currentPage - 1}
          />
        </Flex>
      )}
      <Modal isOpen={isOpen3} onClose={onClose3}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Update Status</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            Is this query is resolved, if YES then make it to inactive.
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="red" mr={3} onClick={onClose3}>
              Cancel
            </Button>
            <Button colorScheme="green" onClick={confirmStatusChange}>
              Save Changes
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Message Modal */}
      {selectedContact && (
        <Modal isOpen={isOpen2} onClose={onClose2} size="lg" isCentered>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>Contact Message</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <Text>{selectedContact.message}</Text>
            </ModalBody>
            <ModalFooter>
              <Button colorScheme="blue" mr={3} onClick={onClose2}>
                Close
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}
    </Layout>
  );
};

export default Contact;
