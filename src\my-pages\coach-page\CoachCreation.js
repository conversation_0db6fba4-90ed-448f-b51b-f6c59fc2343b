import React from "react";
import Layout from "../../layout/default";
import {
  Box,
  Flex,
  Text,
  Tooltip,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
} from "@chakra-ui/react";
import { Link, useNavigate } from "react-router-dom";
import { IoMdArrowRoundBack } from "react-icons/io";
import BasicDetailsCoach from "./BasicDetailsCoach";

const CoachCreation = () => {
  const navigate = useNavigate();
  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="Create Course" content="container">
        <Flex justifyContent={"space-between"} alignItems={"center"} mb={6}>
          <Flex justifyContent={"center"} alignItems={"center"}>
            <Tooltip label="Back">
              <Text
                mr={3}
                as={"span"}
                fontSize={"28px"}
                cursor={"pointer"}
                onClick={() => navigate(-1)}
              >
                <IoMdArrowRoundBack />
              </Text>
            </Tooltip>
            <Breadcrumb fontWeight="medium" fontSize="sm">
              <BreadcrumbItem>
                  <Link to={"/"}>Dashboard</Link>
              </BreadcrumbItem>

              <BreadcrumbItem>
                  <Link to="/coach-page">Coaches</Link>
              </BreadcrumbItem>

              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Create Coach</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
        </Flex>
          {/* Details component */}
          <BasicDetailsCoach coachData={null} />
      </Layout>
    </Box>
  );
};

export default CoachCreation;
