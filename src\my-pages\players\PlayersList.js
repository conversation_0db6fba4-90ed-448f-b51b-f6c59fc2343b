import { Link } from "react-router-dom";
import { useState, useEffect } from "react";
import Layout from "../../layout/default";
import ReactPaginate from "react-paginate";
import "../../style/pagination.css";
import {
  Card,
  TableContainer,
  Table,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
  Spinner,
  Flex,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Text,
  Input,
  Box,
  useToast,
  InputGroup,
  InputRightAddon,
  UnorderedList,
  ListItem,
} from "@chakra-ui/react";
import axios from "axios";
import { IoIosClose, IoMdSearch } from "react-icons/io";
import { IoFilter } from "react-icons/io5";

const PlayerList = () => {
  const [playerDetails, setPlayerDetails] = useState({
    result: [],
    isLoading: false,
    error: false,
    notFound: false,
  });

  const [searchPlayerName, setSearchPlayerName] = useState("");
  const [showSearch, setShowSearch] = useState(false);

  const toast = useToast();

  const token = sessionStorage.getItem("admintoken").split(" ")[1];

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);

  const getPlayers = async (playerName) => {
    setPlayerDetails({
      result: [],
      isLoading: true,
      error: false,
      notFound: false,
    });
    let queryString = "?";

    if (playerName) {
      queryString += `firstName=${playerName}`;
    } else {
      queryString = `?page=${currentPage}`;
    }

    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/player/${
        queryString ? `${queryString}` : ""
      }`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setPlayerDetails({
          result: response.data.data,
          isLoading: false,
          error: false,
          notFound: response.data.data.length === 0 ? true : false,
        });
        setTotalPages(Math.ceil(response.data.totalResults / 25));
      })
      .catch((error) => {
        console.log(error);
        setPlayerDetails({
          result: [],
          isLoading: false,
          error: true,
          notFound: false,
        });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handlePageChange = ({ selected }) => {
    setCurrentPage(selected + 1);
  };

  useEffect(() => {
    getPlayers(searchPlayerName);
  }, [currentPage, searchPlayerName]);

  return (
    <Layout title="Player" content="container">
      <Flex
        w={"100%"}
        justifyContent={"space-between"}
        alignItems={"center"}
        mb={6}
      >
        {showSearch ? (
          <Box flexBasis={"100%"}>
            <InputGroup size="md">
              <Input
                pr="4.5rem"
                type="text"
                placeholder="Search by player first name"
                borderColor={"gray.300"}
                onChange={(e) => {
                  if (e.target.value.length >= 3) {
                    setTimeout(() => {
                      setSearchPlayerName(e.target.value);
                    }, 500);
                  }
                  if (e.target.value.length === 0) {
                    setSearchPlayerName("");
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    if (e.target.value.length >= 3) {
                      setSearchPlayerName(e.target.value);
                    }
                  }
                }}
              />
              <InputRightAddon
                bgColor={"gray.300"}
                border={"1px"}
                borderColor={"gray.300"}
                onClick={() => {
                  setShowSearch(false);
                  setSearchPlayerName("");
                }}
                cursor={"pointer"}
              >
                <IoIosClose fontSize={"24px"} />
              </InputRightAddon>
            </InputGroup>
          </Box>
        ) : (
          <Flex
            flexBasis={"100%"}
            justifyContent={"space-between"}
            alignItems={"center"}
          >
            <Breadcrumb fontWeight="medium" fontSize="sm">
              <BreadcrumbItem>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbItem>

              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Athlete</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
            <Text
              display={"flex"}
              px={4}
              justifyContent={"center"}
              alignItems={"center"}
              py={"7px"}
              border={"1px"}
              borderColor={"gray.300"}
              rounded={"md"}
              color="gray.500"
              cursor={"pointer"}
              onClick={() => setShowSearch(true)}
            >
              <IoMdSearch fontSize={"24px"} />
              <IoFilter fontSize={"22px"} ml={1} />
            </Text>
          </Flex>
        )}
      </Flex>

      <Card>
        {!playerDetails?.isLoading && playerDetails?.error ? (
          <Flex
            justifyContent={"center"}
            alignItems={"center"}
            w={"full"}
            my={10}
          >
            <Text color={"red.500"}>
              Something went wrong please try again later...
            </Text>
          </Flex>
        ) : (
          <TableContainer
            height={`${window.innerHeight - 235}px`}
            overflowY={"scroll"}
          >
            <Table variant="simple">
              <Thead
                bgColor={"#c1eaee"}
                position={"sticky"}
                top={"0px"}
                zIndex={"99"}
              >
                <Tr bgColor={"#E2DFDF"}>
                  <Th>First Name</Th>
                  <Th>Last Name</Th>
                  <Th>Mobile</Th>
                  <Th>Hobbies</Th>
                  <Th>School Name</Th>
                  <Th>Email</Th>
                </Tr>
              </Thead>
              <Tbody>
                {playerDetails?.isLoading && !playerDetails?.error ? (
                  <Tr>
                    <Td></Td>
                    <Td></Td>
                    <Td
                      display={"flex"}
                      justifyContent={"flex-end"}
                      alignItems={"center"}
                    >
                      <Spinner />
                    </Td>
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                  </Tr>
                ) : !playerDetails?.isLoading && playerDetails?.error ? (
                  <Flex
                    justifyContent={"center"}
                    alignItems={"center"}
                    w={"full"}
                    my={10}
                  >
                    <Text color={"red.500"}>
                      Something went wrong please try again later...
                    </Text>
                  </Flex>
                ) : !playerDetails?.notFound ? (
                  playerDetails?.result?.map((player, i) => {
                    return (
                      <Tr key={i}>
                        <Td fontSize={"14px"}>{player?.firstName || "n/a"}</Td>
                        <Td fontSize={"14px"}>{player?.lastName || "n/a"}</Td>
                        <Td fontSize={"14px"}>{player?.mobile || "n/a"}</Td>
                        <Td fontSize={"14px"}>
                          {player?.hobbies?.length > 0 ?
                          <UnorderedList>
                            {player?.hobbies?.map((hobbie, inx) => {
                              return (
                                <ListItem key={inx}>{hobbie?.id?.name}</ListItem>
                              )
                            })}
                          </UnorderedList>
                          :
                          'n/a'
                          }
                        </Td>
                        <Td fontSize={"14px"}>{player?.schoolName || "n/a"}</Td>
                        <Td fontSize={"14px"}>{player?.email || "n/a"}</Td>
                      </Tr>
                    );
                  })
                ) : (
                  <Tr>
                    <Td></Td>
                    <Td></Td>
                    <Td
                      display={"flex"}
                      justifyContent={"flex-end"}
                      alignItems={"center"}
                    >
                      <Text color={"green.500"} fontWeight={"semibold"}>
                        No result found
                      </Text>
                    </Td>
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                  </Tr>
                )}
              </Tbody>
            </Table>
          </TableContainer>
        )}
      </Card>

      {/* Pagination */}
      {!playerDetails?.notFound && (
        <Flex
          justifyContent="center"
          alignItems="center"
          flexDirection={"row"}
          w={"100%"}
          mt={5}
        >
          <ReactPaginate
            previousLabel="Previous"
            nextLabel="Next"
            breakLabel="..."
            pageCount={totalPages}
            marginPagesDisplayed={2}
            pageRangeDisplayed={5}
            onPageChange={handlePageChange}
            containerClassName="pagination"
            activeClassName="active"
          />
        </Flex>
      )}
    </Layout>
  );
};

export default PlayerList;
