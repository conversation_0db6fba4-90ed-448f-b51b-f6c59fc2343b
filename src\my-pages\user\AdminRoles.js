import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";

import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Box,
  Flex,
  Button,
  Text,
  Input,
  Modal,
  FormControl,
  FormLabel,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  Td,
  TableContainer,
  useDisclosure,
  Checkbox,
  useToast,
  Grid,
  Spinner,
  Tooltip,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
} from "@chakra-ui/react";
import axios from "axios";
import { setToast } from "../../functions/Toastfunction";
import ConfirmationModal from "../../functions/Modals/ConfimationModals";
import { Link, useNavigate } from "react-router-dom";
import { IoMdArrowRoundBack } from "react-icons/io";
import { MdDelete, MdEdit } from "react-icons/md";
import { useSelector } from "react-redux";

const AdminRoles = () => {
  const [roleid, setRoleid] = useState(null);
  const [confirmisOpen, setConfirmisOpen] = useState(false);
  const openConfirmModal = (id) => {
    setConfirmisOpen(true);
    setRoleid(id);
  };
  const navigate = useNavigate();
  const closeConfirmModal = () => setConfirmisOpen(false);
  const [showupdatebutton, setShowupdateButton] = useState(false);
  const [render, setRender] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [roles, setRoles] = useState(null);
  const toast = useToast();
  const [roledetail, setRoleDetail] = useState({
    name: "",
    description: "",
  });
  const [rolecontroller, setRoleController] = useState({
    coach: ["read"],
    course: [],
    cms: [],
    player: [],
    user: [],
    user_group: [],
    contact: [],
  });
  let accessroles = [
    "coach",
    "course",
    "cms",
    "player",
    "user",
    "user_group",
    "contact",
  ];

  const userData = useSelector((state) => state.user);

  const handleAddRoleButtonClicked = () => {
    setShowupdateButton(false);
    setRoleController({
      coach: [],
      course: [],
      cms: [],
      player: [],
      user: [],
      user_group: [],
      contact: [],
    });
    setRoleDetail({
      name: "",
      description: "",
    });
    onOpen();
  };
  const handlerolechange = (e) => {
    const { name, value } = e.target;
    // Check if the first character is a space
    if (value.charAt(0) === " ") {
      // Remove the first character (space)
      e.target.value = value.slice(1);
      return;
    }
    setRoleDetail({
      ...roledetail,
      [name]: value,
    });
  };

  const handleAllrole = (e, name) => {
    let newobj = { ...rolecontroller };
    if (newobj[name].length < 3) {
      newobj[name] = [];
      newobj[name].push("read");
      newobj[name].push("write");
      newobj[name].push("delete");
    } else {
      newobj[name] = [];
    }
    setRoleController(newobj);
  };

  const handleReadRole = (e, name) => {
    let newobj = { ...rolecontroller };
    if (e.target.checked) {
      newobj[name].push("read");
    } else {
      newobj[name] = newobj[name].filter((item) => item !== "read");
    }
    setRoleController(newobj);
  };

  const handleWriteRole = (e, name) => {
    let newobj = { ...rolecontroller };
    if (e.target.checked) {
      newobj[name].push("write");
      if (!newobj[name].includes("read")) {
        newobj[name].push("read");
      }
    } else {
      newobj[name] = newobj[name].filter((item) => item !== "write");
    }
    setRoleController(newobj);
  };

  const handleDeleteRole = (e, name) => {
    let newobj = { ...rolecontroller };
    if (e.target.checked) {
      newobj[name].push("delete");
      if (!newobj[name].includes("read")) {
        newobj[name].push("read");
      }
    } else {
      newobj[name] = newobj[name].filter((item) => item !== "delete");
    }
    setRoleController(newobj);
  };

  const [roleLoading, setRoleLoading] = useState(true);
  function getUserRolesfromBackend() {
    setRoleLoading(false);
    const headers = {
      Authorization: sessionStorage.getItem("admintoken"),
    };
    axios({
      method: "GET",
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-user-groups`,
      headers,
    })
      .then((r) => {
        // Handle the nested response structure
        const rolesData = r.data?.data?.userGroups || [];
        console.log(rolesData);
        setRoles(rolesData);
        setRoleLoading(true);
      })
      .catch((err) => {
        if (err.response?.data?.err === "Invalid token") {
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          return;
        }

        if (err.response?.status === 403) {
          setToast(
            toast,
            `You don't have an access to perform this action`,
            "",
            "warning"
          );
        } else {
          setToast(toast, `${err.response?.data?.err || 'An error occurred'}`, "", "error");
        }
        setRoleLoading(true);
      });
  }

  const [createloader, setCreateLoader] = useState(false);
  const handleCreateRoleHitApi = () => {
    if (createloader) {
      return;
    }
    setCreateLoader(true);
    const headers = {
      Authorization: sessionStorage.getItem("admintoken"),
    };
    axios({
      method: "POST",
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-user-groups`,
      data: { ...roledetail, access_scopes: rolecontroller },
      headers,
    })
      .then((r) => {
        setToast(toast, `Role Created Successfully`, "", "success");
        onClose();
        setRender(!render);
        setCreateLoader(false);
      })
      .catch((err) => {
        if (err.response.data.err === "Invalid token") {
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          return;
        }
        setCreateLoader(false);
        if (err.response.status === 403) {
          setToast(
            toast,
            `You don't have an access to perform this action`,
            "",
            "warning"
          );
        } else {
          setToast(toast, `${err.response.data.err}`, "", "error");
        }
      });
  };

  function handleEditRoleButtonClicked(item) {
    let obj = {};
    obj.name = item.name;
    obj.description = item.description;
    setRoleDetail(obj);
    let scope = { ...item.access_scopes };
    setRoleController(scope);
    setShowupdateButton(true);
    setRoleid(item._id);
    onOpen();
  }

  const [updateloader, setUpdateLoader] = useState(false);
  function handleupdateRoleHitApi() {
    if (updateloader) {
      return;
    }
    setUpdateLoader(true);
    const headers = {
      Authorization: sessionStorage.getItem("admintoken"),
    };
    axios({
      method: "PATCH",
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-user-groups/${roleid}`,
      data: { ...roledetail, access_scopes: rolecontroller },
      headers,
    })
      .then((r) => {
        setToast(toast, "Role Updated Successfully", "", "success");
        onClose();
        setRender(!render);
        setUpdateLoader(false);
      })
      .catch((err) => {
        if (err.response.data.err === "Invalid token") {
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          return;
        }
        setUpdateLoader(false);
        if (err.response.status === 403) {
          setToast(
            toast,
            `You don't have an access to perform this action`,
            "",
            "warning"
          );
        } else {
          setToast(toast, `${err.response.data.err}`, "", "error");
        }
      });
  }

  const [deleteloader, setdeleteLoader] = useState(false);
  function handleDeleteRoleButtonClickedApiHit() {
    if (deleteloader) {
      return;
    }
    setdeleteLoader(true);
    const headers = {
      Authorization: sessionStorage.getItem("admintoken"),
    };
    axios({
      method: "DELETE",
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-user-groups/${roleid}`,
      headers,
    })
      .then((r) => {
        setdeleteLoader(false);
        setToast(toast, "Role Deleted Successfully", "", "success");
        setRender(!render);
        closeConfirmModal();
      })
      .catch((err) => {
        if (err.response.data.err === "Invalid token") {
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          return;
        }
        setdeleteLoader(false);
        if (err.response.status === 403) {
          setToast(
            toast,
            `You don't have an access to perform this action`,
            "",
            "warning"
          );
        } else {
          setToast(toast, `${err.response.data.err}`, "", "error");
        }
      });
  }

  useEffect(() => {
    getUserRolesfromBackend();
  }, [render]);

  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="Role | Administration" content="container">
        <Flex justifyContent={"space-between"} alignItems={"center"} mb={6}>
          <Flex justifyContent={"center"} alignItems={"center"}>
            <Tooltip label="Back">
              <Text
                mr={3}
                as={"span"}
                fontSize={"28px"}
                cursor={"pointer"}
                onClick={() => navigate(-1)}
              >
                <IoMdArrowRoundBack />
              </Text>
            </Tooltip>
            <Breadcrumb fontWeight="medium" fontSize="sm">
              <BreadcrumbItem>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbItem>

              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">User Roles</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          {roleLoading
            ? userData?.accessScopes?.user_group?.includes("write") && (
                <Flex justifyContent="space-between">
                  <Flex gap="10px">
                    <Button
                      variant={"outline"}
                      colorScheme="teal"
                      size={"sm"}
                      py={5}
                      px={4}
                      onClick={handleAddRoleButtonClicked}
                    >
                      Add Roles
                    </Button>
                  </Flex>
                </Flex>
              )
            : null}
        </Flex>
        {roleLoading ? (
          <Box width="100%">
            <Modal
              closeOnOverlayClick={false}
              isOpen={isOpen}
              onClose={onClose}
              p={3}
              size="lg"
            >
              <ModalOverlay />
              <ModalContent>
                <ModalHeader>
                  {showupdatebutton ? "Update Role" : "Create New Role"}
                </ModalHeader>
                <ModalCloseButton />
                <ModalBody p={6} textAlign="left">
                  <FormControl mb="10px">
                    <FormLabel>Role Name</FormLabel>
                    <Input
                      name="name"
                      onChange={handlerolechange}
                      value={roledetail.name}
                      placeholder="Enter Role"
                    />
                  </FormControl>

                  <FormControl mb="10px">
                    <FormLabel>Description</FormLabel>
                    <Input
                      name="description"
                      onChange={handlerolechange}
                      value={roledetail.description}
                      placeholder="Enter description of the Role"
                    />
                  </FormControl>

                  <FormControl mb="10px">
                    <FormLabel>Select Access Control</FormLabel>
                    <TableContainer>
                      <Table size="sm">
                        <Thead>
                          <Tr>
                            <Th> </Th>
                            <Th>
                              <Text>Read</Text>
                            </Th>
                            <Th>
                              <Text>Write</Text>
                            </Th>
                            <Th>
                              <Text>Delete</Text>
                            </Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {accessroles.map((item, i) => (
                            <Tr key={i}>
                              <Td>
                                <FormControl>
                                  <Flex gap="10px">
                                    <Checkbox
                                      isChecked={
                                        rolecontroller[item]?.length === 3
                                      }
                                      onChange={(e) => handleAllrole(e, item)}
                                    />
                                    <FormLabel>
                                      {item.split("_").join(" ")}
                                    </FormLabel>
                                  </Flex>
                                </FormControl>
                              </Td>
                              <Td>
                                <Checkbox
                                  isDisabled={rolecontroller[item]?.some(
                                    (x) =>
                                      x === "write" ||
                                      x === "delete" ||
                                      (item === "coach" &&
                                        rolecontroller[item]?.includes("read"))
                                  )}
                                  isChecked={rolecontroller[item]?.includes(
                                    "read"
                                  )}
                                  onChange={(e) => handleReadRole(e, item)}
                                />
                              </Td>
                              <Td>
                                <Checkbox
                                  isChecked={rolecontroller[item]?.includes(
                                    "write"
                                  )}
                                  onChange={(e) => handleWriteRole(e, item)}
                                />
                              </Td>
                              <Td>
                                <Checkbox
                                  isChecked={rolecontroller[item]?.includes(
                                    "delete"
                                  )}
                                  onChange={(e) => handleDeleteRole(e, item)}
                                />
                              </Td>
                            </Tr>
                          ))}
                        </Tbody>
                      </Table>
                    </TableContainer>
                  </FormControl>

                  <Grid gap="15px" templateColumns="repeat(3,1fr)"></Grid>
                </ModalBody>
                <ModalFooter gap="10px">
                  {showupdatebutton ? (
                    <Button
                      onClick={handleupdateRoleHitApi}
                      variant={"outline"}
                      colorScheme="telegram"
                      size={"sm"}
                      py={5}
                      px={4}
                    >
                      {updateloader ? <Spinner /> : "Update"}
                    </Button>
                  ) : (
                    <Button
                      variant={"outline"}
                      colorScheme="green"
                      size={"sm"}
                      py={5}
                      px={4}
                      mr={1}
                      onClick={handleCreateRoleHitApi}
                    >
                      {createloader ? <Spinner /> : "Save"}
                    </Button>
                  )}

                  <Button
                    onClick={onClose}
                    variant={"outline"}
                    colorScheme="red"
                    size={"sm"}
                    py={5}
                    px={4}
                  >
                    Cancel
                  </Button>
                </ModalFooter>
              </ModalContent>
            </Modal>

            <ConfirmationModal
              heading="Delete Role"
              action="Are you Sure? You want to Delete this Role"
              ConfirmButton="Yes Delete"
              onClickFunction={handleDeleteRoleButtonClickedApiHit}
              isOpen={confirmisOpen}
              onClose={closeConfirmModal}
              loader={true}
              loading={deleteloader}
            />

            <div
              style={{
                borderRadius: "7px",
                boxShadow:
                  "rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px",
                backgroundColor: "white",
              }}
              height="30px"
              width="30px"
            >
              <TableContainer
                height={`${window.innerHeight - 185}px`}
                overflowY={"scroll"}
              >
                <Table variant="simple">
                  <Thead
                    bgColor={"#c1eaee"}
                    position={"sticky"}
                    top={"0px"}
                    zIndex={"99"}
                  >
                    <Tr bgColor={"#E2DFDF"}>
                      <Th>S.No</Th>
                      <Th>Name</Th>
                      <Th>Description</Th>

                      <Th>Action</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {roles?.map((item, i) => (
                      <Tr key={i}>
                        <Td
                          style={{
                            whiteSpace: "pre-wrap",
                            wordWrap: "break-word",
                          }}
                          fontSize={"14px"}
                        >
                          {i + 1}.
                        </Td>
                        <Td
                          style={{
                            whiteSpace: "pre-wrap",
                            wordWrap: "break-word",
                          }}
                          fontSize={"14px"}
                        >
                          {item.name}
                        </Td>

                        <Td
                          style={{
                            whiteSpace: "pre-wrap",
                            wordWrap: "break-word",
                          }}
                          fontSize={"14px"}
                        >
                          {item.description || "n/a"}
                        </Td>
                        <Td fontSize={"14px"}>
                          {item.name !== "Super Admin" ? (
                            <Flex>
                              {userData?.accessScopes?.user_group?.includes(
                                "write"
                              ) && (
                                <Flex
                                  onClick={() =>
                                    handleEditRoleButtonClicked(item)
                                  }
                                  mr={1}
                                  cursor={"pointer"}
                                >
                                  <Tooltip label="Edit User">
                                    <Text>
                                      <MdEdit fontSize={"24px"} />
                                    </Text>
                                  </Tooltip>
                                </Flex>
                              )}
                              {userData?.accessScopes?.user_group?.includes(
                                "delete"
                              ) && (
                                <Flex
                                  cursor={"pointer"}
                                  onClick={() => openConfirmModal(item._id)}
                                >
                                  <Tooltip label="Delete User">
                                    <Text>
                                      <MdDelete fontSize={"24px"} />
                                    </Text>
                                  </Tooltip>
                                </Flex>
                              )}
                            </Flex>
                          ) : (
                            "--"
                          )}
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </TableContainer>
            </div>
          </Box>
        ) : (
          <Flex justifyContent={"center"} alignItems={"center"} mt={16}>
            <Spinner size={"lg"} />
          </Flex>
        )}
      </Layout>
    </Box>
  );
};

export default AdminRoles;
