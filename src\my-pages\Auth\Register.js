import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Heading,
  Avatar,
  IconButton,
  useToast,
  Image,
  Tag,
  TagLabel,
  TagCloseButton,
  Divider,
  Text,
  InputGroup,
  InputRightElement,
  VStack,
  HStack,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Button as ChakraButton,
  Checkbox,
  CheckboxGroup,
  useDisclosure,
  Portal
} from "@chakra-ui/react";
import LogoImage from "../../assets/images/mask/MainKhelCoach.svg";
import { FiPlus, FiTrash, FiChevronDown } from "react-icons/fi";
import { useNavigate } from "react-router-dom";
import { uploadImage } from "../../utilities/uploadImage";
import axios from "axios";

const Register = () => {
  const [profileImage, setProfileImage] = useState(null);
  const [academyImages, setAcademyImages] = useState([]);
  const [panFile, setPanFile] = useState(null);
  const [aadhaarFile, setAadhaarFile] = useState(null);
  const [categories, setCategories] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [categoryDropdownOpen, setCategoryDropdownOpen] = useState(false);
  const [academyName, setAcademyName] = useState("");
  const [companyRegNo, setCompanyRegNo] = useState("");
  const toast = useToast();
  const navigate = useNavigate();
  const categoryInputRef = useRef();
  const [showPassword, setShowPassword] = useState(false);

  // Placeholder state for all fields
  const [form, setForm] = useState({
    email: "",
    password: "",
    phone: "",
    officeAddress: "",
    facilityAddresses: [""],
    panNumber: "",
    accountNo: "",
    ifsc: "",
    gstNumber: "",
    officeAddressLine2: "",
    city: "",
    state: "",
    pinCode: "",
    country: "",
    aadhaarNumber: "",
  });

  useEffect(() => {
    axios.get(`${process.env.REACT_APP_BASE_URL}/api/category?page=1`)
      .then(res => {
        setCategories(res.data.data || []);
      })
      .catch(err => {
        toast({ title: "Failed to fetch categories", status: "error" });
      });
  }, [toast]);

  // Handle click outside for category dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (categoryInputRef.current && !categoryInputRef.current.contains(event.target)) {
        setCategoryDropdownOpen(false);
      }
    }
    if (categoryDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [categoryDropdownOpen]);

  const handleRegister = async () => {
    try {
      // Upload profile image if present
      let profileImageUrl = null;
      if (profileImage) {
        const res = await uploadImage(profileImage);
        profileImageUrl = res.url || res;
      }
      // Upload academy images if present
      let academyImageUrls = [];
      if (academyImages && academyImages.length > 0) {
        for (const img of academyImages) {
          const res = await uploadImage(img);
          academyImageUrls.push(res.url || res);
        }
      }
      // Upload PAN image if present
      let panImageUrl = null;
      if (panFile) {
        const res = await uploadImage(panFile);
        panImageUrl = res.url || res;
      } else {
        panImageUrl = "https://example.com/pan.jpg"; // Example URL
      }

      // Upload Aadhaar image if present
      let aadhaarImageUrl = null;
      if (aadhaarFile) {
        const res = await uploadImage(aadhaarFile);
        aadhaarImageUrl = res.url || res;
      }

      // Map selectedCategories (IDs) to names
      const sportsCategories = selectedCategories
        .map(cid => categories.find(cat => cat._id === cid)?.name)
        .filter(Boolean);

      // Prepare office address with all required fields
      const officeAddress = {
        addressLine1: form.officeAddress,
        addressLine2: form.officeAddressLine2,
        city: form.city,
        state: form.state,
        pinCode: form.pinCode,
        country: form.country
      };

      // Prepare linkedFacilities with all required fields
      const linkedFacilities = form.facilityAddresses.map(addr => ({
        name: addr.name,
        addressLine1: addr.addressLine1,
        addressLine2: addr.addressLine2,
        city: addr.city,
        state: addr.state,
        pinCode: addr.pinCode,
        country: addr.country,
        amenities: addr.amenities,
        location: {
          type: "Point",
          coordinates: [77.5946, 12.9716], // Example coordinates
          is_location_exact: true
        }
      }));

      // Prepare bank details
      const bankDetails = {
        accountNumber: form.accountNo,
        accountHolderName: academyName,
        ifsc: form.ifsc
      };

      // Ensure aadhaarNumber is provided
      const aadhaarNumber = form.aadhaarNumber;

      // Prepare payload with all required fields
      const payload = {
        name: academyName,
        mobile: form.phone,
        email: form.email,
        gstNumber: form.gstNumber,
        academyImages: academyImageUrls,
        profileImage: profileImageUrl,
        sportsCategories,
        officeAddress,
        companyRegistrationNumber: companyRegNo,
        linkedFacilities,
        password: form.password,
        bankDetails,
        panNumber: form.panNumber,
        panImage: panImageUrl,
        aadhaarNumber: aadhaarNumber,
        aadhaarImage: aadhaarImageUrl
      };
console.log("payload",payload)

      // Get Bearer token from sessionStorage
      const token = sessionStorage.getItem("admintoken");

      // Make API call
      await axios.post(`${process.env.REACT_APP_BASE_URL}/api/academy`, payload, {
        headers: {
          Authorization: token,
          "Content-Type": "application/json"
        }
      });

      toast({ title: "Registration successful!", status: "success" });
      navigate("/verification-pending");
    } catch (err) {
      toast({ title: err?.response?.data?.message || "Registration failed", status: "error" });
    }
  };

  const handleFacilityAddressChange = (index, value) => {
    const updated = [...form.facilityAddresses];
    updated[index] = value;
    setForm({ ...form, facilityAddresses: updated });
  };

  const addFacilityAddress = () => {
    setForm({ ...form, facilityAddresses: [...form.facilityAddresses, ""] });
  };

  const removeFacilityAddress = (index) => {
    const updated = form.facilityAddresses.filter((_, i) => i !== index);
    setForm({ ...form, facilityAddresses: updated });
  };

  // Image handlers
  const handleProfileImage = e => {
    if (e.target.files[0]) setProfileImage(e.target.files[0]);
  };
  const removeProfileImage = () => setProfileImage(null);

  const handleAcademyImages = e => {
    setAcademyImages([...academyImages, ...Array.from(e.target.files)]);
  };
  const removeAcademyImage = idx => {
    setAcademyImages(academyImages.filter((_, i) => i !== idx));
  };

  const handlePanFile = e => {
    if (e.target.files[0]) setPanFile(e.target.files[0]);
  };
  const removePanFile = () => setPanFile(null);

  const handleAadhaarFile = e => {
    if (e.target.files[0]) setAadhaarFile(e.target.files[0]);
  };
  const removeAadhaarFile = () => setAadhaarFile(null);

  // Sports categories handlers
  const handleCategorySelect = id => {
    setSelectedCategories(prev =>
      prev.includes(id) ? prev.filter(cid => cid !== id) : [...prev, id]
    );
  };
  const removeCategory = id => {
    setSelectedCategories(selectedCategories.filter(cid => cid !== id));
  };

  const handleShowClick = () => setShowPassword(!showPassword);

  return (
    <Flex minH="100vh" align="center" justify="center" bg="gray.100" direction="column" py={6}>
      <Image src={LogoImage} alt="logo" w={"180px"} mb={4} />
      <Box bg="white" p={{ base: 4, md: 8 }} rounded="lg" w={{ base: "100%", md: "650px" }} boxShadow="lg">
        <Heading mb={2} textAlign="center" fontSize="2xl">Academy Registration</Heading>
        <Text mb={6} color="gray.500" textAlign="center">Fill in the details to register your academy</Text>
        <Stack spacing={6} divider={<Divider />}> 
          {/* Profile Section */}
          <Flex gap={8} align="flex-start" direction={{ base: "column", md: "row" }}>
            <Box minW="160px">
              <FormLabel fontWeight="bold">Profile Image</FormLabel>
              <Avatar size="xl" src={profileImage ? URL.createObjectURL(profileImage) : undefined} mb={2} />
              <Flex gap={2} mb={2}>
                <Input type="file" accept="image/*" display="none" id="profile-image-upload" onChange={handleProfileImage} />
                <Button as="label" htmlFor="profile-image-upload" leftIcon={<FiPlus />}>Upload</Button>
                <Button ml={0} colorScheme="red" leftIcon={<FiTrash />} onClick={removeProfileImage} variant="outline" disabled={!profileImage}>Remove</Button>
              </Flex>
            </Box>
            <Stack flex={1} spacing={4}>
              <FormControl>
                <FormLabel fontWeight="bold">Academy Name</FormLabel>
                <Input value={academyName} placeholder="Enter academy name" onChange={e => setAcademyName(e.target.value)} />
              </FormControl>
              <FormControl>
                <FormLabel fontWeight="bold">Email</FormLabel>
                <Input value={form.email} placeholder="Email address" onChange={e => setForm({ ...form, email: e.target.value })} />
              </FormControl>
            </Stack>
          </Flex>

          {/* Account Details */}
          <HStack spacing={4} align="flex-start">
            <FormControl>
              <FormLabel fontWeight="bold">Password</FormLabel>
              <InputGroup>
                <Input
                  type={showPassword ? "text" : "password"}
                  value={form.password}
                  placeholder="Password"
                  onChange={e => setForm({ ...form, password: e.target.value })}
                />
                <InputRightElement width="4.5rem">
                  <Button h="1.75rem" size="sm" onClick={handleShowClick}>
                    {showPassword ? "Hide" : "Show"}
                  </Button>
                </InputRightElement>
              </InputGroup>
            </FormControl>
            <FormControl>
              <FormLabel fontWeight="bold">Phone Number</FormLabel>
              <Input value={form.phone} placeholder="Phone Number" onChange={e => setForm({ ...form, phone: e.target.value })} />
            </FormControl>
          </HStack>

          {/* Company Registration Number */}
          <FormControl>
            <FormLabel fontWeight="bold">Company Registration Number</FormLabel>
            <Input value={companyRegNo} placeholder="Enter company registration number" onChange={e => setCompanyRegNo(e.target.value)} />
          </FormControl>

          {/* Office Address Section */}
          <Box>
            <FormLabel fontWeight="bold">Office Address</FormLabel>
            <VStack align="stretch" spacing={2}>
              <Input value={form.officeAddress} placeholder="Office Address Line 1" onChange={e => setForm({ ...form, officeAddress: e.target.value })} />
              <Input value={form.officeAddressLine2} placeholder="Office Address Line 2" onChange={e => setForm({ ...form, officeAddressLine2: e.target.value })} />
              <Input value={form.city} placeholder="City" onChange={e => setForm({ ...form, city: e.target.value })} />
              <Input value={form.state} placeholder="State" onChange={e => setForm({ ...form, state: e.target.value })} />
              <Input value={form.pinCode} placeholder="Pin Code" onChange={e => setForm({ ...form, pinCode: e.target.value })} />
              <Input value={form.country} placeholder="Country" onChange={e => setForm({ ...form, country: e.target.value })} />
            </VStack>
          </Box>

          {/* Facilities Section */}
          <Box>
            <FormLabel fontWeight="bold">Facilities</FormLabel>
            {form.facilityAddresses.map((address, idx) => (
              <Box key={idx} mt={4} p={4} borderWidth={1} borderRadius="md">
                <FormControl>
                  <FormLabel fontWeight="bold">Facility Name</FormLabel>
                  <Input value={address.name} placeholder="Facility Name" onChange={e => handleFacilityAddressChange(idx, { ...address, name: e.target.value })} />
                </FormControl>
                <Input value={address.addressLine1} placeholder="Facility Address Line 1" onChange={e => handleFacilityAddressChange(idx, { ...address, addressLine1: e.target.value })} />
                <Input value={address.addressLine2} placeholder="Facility Address Line 2" onChange={e => handleFacilityAddressChange(idx, { ...address, addressLine2: e.target.value })} />
                <Input value={address.city} placeholder="City" onChange={e => handleFacilityAddressChange(idx, { ...address, city: e.target.value })} />
                <Input value={address.state} placeholder="State" onChange={e => handleFacilityAddressChange(idx, { ...address, state: e.target.value })} />
                <Input value={address.pinCode} placeholder="Pin Code" onChange={e => handleFacilityAddressChange(idx, { ...address, pinCode: e.target.value })} />
                <Input value={address.country} placeholder="Country" onChange={e => handleFacilityAddressChange(idx, { ...address, country: e.target.value })} />
                <Input value={address.amenities} placeholder="Amenities" onChange={e => handleFacilityAddressChange(idx, { ...address, amenities: e.target.value })} />
                <Button size="sm" colorScheme="red" onClick={() => removeFacilityAddress(idx)} leftIcon={<FiTrash />}>Remove Facility</Button>
              </Box>
            ))}
            <Button size="sm" colorScheme="teal" onClick={addFacilityAddress} leftIcon={<FiPlus />}>Add Facility</Button>
          </Box>

          {/* Documents Section */}
          <HStack spacing={4} align="flex-end">
            <FormControl>
              <FormLabel fontWeight="bold">PAN Number</FormLabel>
              <Input value={form.panNumber} placeholder="Enter PAN No." onChange={e => setForm({ ...form, panNumber: e.target.value })} />
            </FormControl>
            <Box>
              <FormLabel fontWeight="bold">PAN Image</FormLabel>
              <Input type="file" accept="image/*" display="none" id="pan-image-upload" onChange={handlePanFile} />
              <Button as="label" htmlFor="pan-image-upload" leftIcon={<FiPlus />}>Upload</Button>
              {panFile && (
                <Button ml={2} colorScheme="red" leftIcon={<FiTrash />} onClick={removePanFile} variant="outline">Remove</Button>
              )}
              {panFile && <Image src={URL.createObjectURL(panFile)} alt="PAN" boxSize="60px" mt={2} borderRadius="md" />}
            </Box>
          </HStack>

          <HStack spacing={4} align="flex-end">
            <FormControl>
              <FormLabel fontWeight="bold">Aadhaar Number</FormLabel>
              <Input value={form.aadhaarNumber} placeholder="Enter Aadhaar No." onChange={e => setForm({ ...form, aadhaarNumber: e.target.value })} />
            </FormControl>
            <Box>
              <FormLabel fontWeight="bold">Aadhaar Image</FormLabel>
              <Input type="file" accept="image/*" display="none" id="aadhaar-image-upload" onChange={handleAadhaarFile} />
              <Button as="label" htmlFor="aadhaar-image-upload" leftIcon={<FiPlus />}>Upload</Button>
              {aadhaarFile && (
                <Button ml={2} colorScheme="red" leftIcon={<FiTrash />} onClick={removeAadhaarFile} variant="outline">Remove</Button>
              )}
              {aadhaarFile && <Image src={URL.createObjectURL(aadhaarFile)} alt="Aadhaar" boxSize="60px" mt={2} borderRadius="md" />}
            </Box>
          </HStack>

          {/* Bank Details Section */}
          <HStack spacing={4} align="flex-end">
            <FormControl>
              <FormLabel fontWeight="bold">Account No.</FormLabel>
              <Input value={form.accountNo} placeholder="Enter account no." onChange={e => setForm({ ...form, accountNo: e.target.value })} />
            </FormControl>
            <FormControl>
              <FormLabel fontWeight="bold">IFSC Code</FormLabel>
              <Input value={form.ifsc} placeholder="Enter IFSC code" onChange={e => setForm({ ...form, ifsc: e.target.value })} />
            </FormControl>
          </HStack>

          {/* Additional Information Section */}
          <FormControl>
            <FormLabel fontWeight="bold">GST Number</FormLabel>
            <Input value={form.gstNumber} placeholder="Enter GST Number" onChange={e => setForm({ ...form, gstNumber: e.target.value })} />
          </FormControl>
          <Box ref={categoryInputRef} position="relative">
            <FormLabel fontWeight="bold">Sports Categories</FormLabel>
            <InputGroup>
              <Input
                readOnly
                value={selectedCategories
                  .map(cid => categories.find(cat => cat._id === cid)?.name)
                  .filter(Boolean)
                  .join(", ")}
                placeholder="Select sports categories"
                onClick={() => setCategoryDropdownOpen(!categoryDropdownOpen)}
                cursor="pointer"
                bg="white"
              />
              <InputRightElement pointerEvents="none">
                <FiChevronDown />
              </InputRightElement>
            </InputGroup>
            <Flex mt={2} gap={2} wrap="wrap">
              {selectedCategories.map(cid => {
                const cat = categories.find(cat => cat._id === cid);
                return cat ? (
                  <Tag key={cid} borderRadius="full" variant="solid" colorScheme="teal">
                    <Image src={cat.image} alt={cat.name} boxSize="20px" borderRadius="full" mr={1} />
                    <TagLabel>{cat.name}</TagLabel>
                    <TagCloseButton onClick={() => removeCategory(cid)} />
                  </Tag>
                ) : null;
              })}
            </Flex>
            {categoryDropdownOpen && (
              <Box position="absolute" zIndex={10} bg="white" borderWidth={1} borderRadius="md" boxShadow="lg" mb={2} w="100%" maxH="250px" overflowY="auto" p={4} bottom="100%">
                <CheckboxGroup value={selectedCategories}>
                  <Stack>
                    {categories.map(cat => (
                      <Checkbox
                        key={cat._id}
                        value={cat._id}
                        isChecked={selectedCategories.includes(cat._id)}
                        onChange={() => handleCategorySelect(cat._id)}
                      >
                        <Flex align="center" gap={2}>
                          <Image src={cat.image} alt={cat.name} boxSize="24px" borderRadius="full" />
                          {cat.name}
                        </Flex>
                      </Checkbox>
                    ))}
                  </Stack>
                </CheckboxGroup>
              </Box>
            )}
          </Box>
        </Stack>
        <Button colorScheme="teal" mt={8} w="full" size="lg" onClick={handleRegister} fontWeight="bold">
          Register
        </Button>
      </Box>
    </Flex>
  );
};

export default Register; 