import { useState } from "react";
import {
  Flex,
  <PERSON>ing,
  Input,
  Button,
  InputGroup,
  Stack,
  InputLeftElement,
  chakra,
  Box,
  FormControl,
  InputRightElement,
  FormErrorMessage,
  useToast,
  Image,
} from "@chakra-ui/react";
import { FaUserAlt, FaLock } from "react-icons/fa";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";
import { setCookie } from "../../utilities/auth";
import { useNavigate } from "react-router-dom";
import LogoImage from "../../assets/images/mask/MainKhelCoach.svg";
import { useDispatch } from "react-redux";
import { loginSuccess, loginFailure } from "./AuthSlice";

const CFaUserAlt = chakra(FaUserAlt);
const CFaLock = chakra(FaLock);

const Login = ({ renderMe }) => {
  const [showPassword, setShowPassword] = useState(false);

  const handleShowClick = () => setShowPassword(!showPassword);

  const [isBtnLoading, setIsBtnLoading] = useState(false);

  const dispatch = useDispatch();

  const toast = useToast();
  const navigate = useNavigate();

  const validationSchema = Yup.object().shape({
    email: Yup.string()
      .email("Enter a valid email")
      .required("Email is required"),
    password: Yup.string().required("Password is required"),
  });

  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      setIsBtnLoading(true);
      let data = JSON.stringify({
        email: values.email,
        password: values.password,
      });

      let config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/academy/login`,
        headers: {
          "Content-Type": "application/json",
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {
          setIsBtnLoading(false);
          const resData = response.data;
          if (resData.responseCode === 0 && resData.status === "success") {
            const user = resData.data.user.user;
            const accessToken = resData.data.user.accessToken;
            sessionStorage.setItem("admintoken", `Bearer ${accessToken}`);
            setCookie("userName", user.name);
            const authStatus = user.academyId?.authStatus;
            if (authStatus === "authorized") {
              navigate("/dashboard");
            } else {
              navigate("/verification-pending");
            }
            dispatch(
              loginSuccess({
                token: accessToken,
                user: {
                  _id: user.id,
                  name: user.name,
                  email: user.email,
                  academyId: user.academyId?._id,
                  accessScopes: user.accessScopes,
                  academyUserGroups: user.academyUserGroups,
                },
              })
            );
            renderMe();
            toast({
              title: `Welcome ${user.name}`,
              status: "success",
              duration: 3000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: resData.message || "Login failed",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        })
        .catch((error) => {
          setIsBtnLoading(false);
          let errMsg = "Something went wrong, please try again later";
          if (error.response && error.response.data && error.response.data.message) {
            errMsg = error.response.data.message;
          }
          toast({
            title: errMsg,
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        });
    },
  });

  return (
    <>
      <Flex
        flexDirection="column"
        width="100wh"
        height="100vh"
        backgroundColor="gray.200"
        justifyContent="center"
        alignItems="center"
      >
        <Stack
          flexDir="column"
          mb="2"
          justifyContent="center"
          alignItems="center"
        >
          <Image src={LogoImage} alt="logo" w={"200px"} mb={4} />
          <form onSubmit={formik.handleSubmit}>
            <Box minW={{ base: "90%", md: "468px" }}>
              <Stack
                spacing={4}
                p="1rem"
                backgroundColor="whiteAlpha.900"
                boxShadow="md"
              >
                <FormControl
                  isInvalid={formik.errors.email && formik.touched.email}
                >
                  <InputGroup>
                    <InputLeftElement
                      pointerEvents="none"
                      children={<CFaUserAlt color="gray.300" />}
                    />
                    <Input
                      type="email"
                      placeholder="Enter email"
                      id="email"
                      name="email"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.email}
                    />
                  </InputGroup>
                  <FormErrorMessage>{formik.errors.email}</FormErrorMessage>
                </FormControl>
                <FormControl
                  isInvalid={formik.errors.password && formik.touched.password}
                >
                  <InputGroup>
                    <InputLeftElement
                      pointerEvents="none"
                      color="gray.300"
                      children={<CFaLock color="gray.300" />}
                    />
                    <Input
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter password"
                      id="password"
                      name="password"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.password}
                    />
                    <InputRightElement width="4.5rem">
                      <Button h="1.75rem" size="sm" onClick={handleShowClick}>
                        {showPassword ? "Hide" : "Show"}
                      </Button>
                    </InputRightElement>
                  </InputGroup>
                  <FormErrorMessage>{formik.errors.password}</FormErrorMessage>
                </FormControl>
                <Flex justifyContent="space-between" alignItems="center" mb={2}>
                  <Box fontSize="sm">
                    Not a member?{" "}
                    <chakra.span
                      color="teal.500"
                      cursor="pointer"
                      onClick={() => navigate("/register")}
                    >
                      Sign Up
                    </chakra.span>
                  </Box>
                  <Box
                    fontSize="sm"
                    color="teal.500"
                    cursor="pointer"
                    onClick={() => navigate("/forgot-password")}
                  >
                    Forgot password?
                  </Box>
                </Flex>
                <Button
                  borderRadius={0}
                  type="submit"
                  variant="solid"
                  colorScheme="teal"
                  width="full"
                  isLoading={isBtnLoading}
                >
                  Login
                </Button>
              </Stack>
            </Box>
          </form>
        </Stack>
      </Flex>
    </>
  );
};

export default Login;