import React, { useState } from 'react'
import {
  Modal,
  ModalOverlay,
  ModalContent,
  Modal<PERSON>eader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Button,
  Input
} from '@chakra-ui/react'

const ReasonModal = (props) => {
    const { isOpen, onOpen, onClose } = useDisclosure()
    const [reason,setReason]=useState("")

    function handleGoBack(){
        //("clicked")
        setReason("")
        onClose()
    }
    //(reason,"reason")
  return (
    <div>
         <Button onClick={onOpen}
         
            border="1px solid teal"
         fontSize={{ base: "12px", md: "16px", lg: "16px" }}
         colorScheme={props?.buttoncolor}
         variant={props?.buttonvariant}
         >{props?.button}</Button>

<Modal closeOnOverlayClick={false}
isOpen={isOpen} onClose={onClose}>
  <ModalOverlay />
  <ModalContent>
    <ModalHeader>{props.title}</ModalHeader>
    <ModalCloseButton  onClick={handleGoBack}/>
    <ModalBody>
    <Input placeholder={props.placeholder}
    onChange={(e)=>setReason(e.target.value)}/>
    </ModalBody>

    <ModalFooter>
      <Button colorScheme='blue' 
      
      isDisabled={reason===""}
      mr={3} onClick={handleGoBack}>
        Submit
      </Button>
      <Button variant='ghost' onClick={handleGoBack}>Cancel</Button>
    </ModalFooter>
  </ModalContent>
</Modal>
    </div>
  )
}

export default ReasonModal