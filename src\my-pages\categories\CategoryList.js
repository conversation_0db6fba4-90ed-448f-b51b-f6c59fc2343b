import { <PERSON>, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import Layout from "../../layout/default";
import ReactPaginate from "react-paginate";
import "../../style/pagination.css";
import {
  But<PERSON>,
  Heading,
  useToast,
  Card,
  TableContainer,
  Table,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
  Spinner,
  Flex,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Text,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  CardBody,
  Image,
  Stack,
  Tooltip,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogCloseButton,
  Input,
  InputGroup,
  InputRightAddon,
} from "@chakra-ui/react";
import axios from "axios";
import { Md<PERSON><PERSON><PERSON>, MdEdit, MdRemoveRedEye } from "react-icons/md";
import { useSelector } from "react-redux";
import { IoIosClose, IoMdSearch } from "react-icons/io";
import { IoFilter } from "react-icons/io5";

const CategoryList = () => {
  const [categories, setCategories] = useState({
    result: [],
    isLoading: false,
    error: false,
    notFound: false,
  });
  const [viewCategory, setViewCategory] = useState({
    name: "",
    des: "",
    img: "",
  });
  const [renderOnChange, setRenderOnChange] = useState(0);
  const [deleteCatId, setDeleteCatId] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);

  const [showSearch, setShowSearch] = useState(false);
  const [searchCategoryName, setSearchCategoryName] = useState("");

  const { isOpen, onOpen, onClose } = useDisclosure();

  const [isOpen1, setIsOpen1] = useState(false);
  const onClose1 = () => setIsOpen1(false);
  const onOpen1 = () => setIsOpen1(true);

  const navigate = useNavigate();
  const toast = useToast();
  const token = sessionStorage?.getItem("admintoken")?.split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getCategories = async (name) => {
    setCategories({
      result: [],
      isLoading: true,
      error: false,
      notFound: false,
    });

    let queryString = "?";

    if (name) {
      queryString += `name=${name}`;
    } else {
      queryString += `?page=${currentPage}`;
    }

    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/category/${
        queryString ? `${queryString}` : ""
      }`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setCategories({
          result: response?.data?.data,
          isLoading: false,
          error: false,
          notFound: !(response?.data?.data?.length > 0),
        });
        setTotalPages(Math.ceil(response.data.totalResults / 25));
      })
      .catch((error) => {
        console.log(error);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
        setCategories({
          result: [],
          isLoading: false,
          error: true,
          notFound: false,
        });
      });
  };

  const handlePageChange = ({ selected }) => {
    setCurrentPage(selected + 1);
  };

  const deleteCategory = () => {
    let config = {
      method: "delete",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/category/${deleteCatId}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    axios
      .request(config)
      .then((response) => {
        setRenderOnChange((prev) => prev + 1);
        setDeleteCatId("");
        toast({
          title: "Category deleted successfully",
          status: "success",
          duration: 3500,
          position: "top",
          isClosable: true,
        });
        setViewCategory({
          name: "",
          des: "",
          img: "",
        });
        onClose1();
      })
      .catch((error) => {
        console.log(error);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
        setViewCategory({
          name: "",
          des: "",
          img: "",
        });
        onClose1();
        setDeleteCatId("");
      });
  };

  useEffect(() => {
    getCategories(searchCategoryName);
  }, [renderOnChange, currentPage, searchCategoryName]);

  return (
    <Layout title="Category" content="container">
      <Flex
        w={"100%"}
        justifyContent={"space-between"}
        alignItems={"center"}
        mb={6}
      >
        {showSearch ? (
          <Flex flexBasis={"100%"}>
            <InputGroup size="md">
              <Input
                pr="4.5rem"
                type="text"
                placeholder="Search"
                borderColor={"gray.300"}
                onChange={(e) => {
                  if (e.target.value.length >= 3) {
                    setTimeout(() => {
                      setSearchCategoryName(e.target.value);
                    }, 500);
                  }
                  if (e.target.value.length === 0) {
                    setSearchCategoryName("");
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    if (e.target.value.length >= 3) {
                      setSearchCategoryName(e.target.value);
                    }
                  }
                }}
              />
              <InputRightAddon
                bgColor={"gray.300"}
                border={"1px"}
                borderColor={"gray.300"}
                onClick={() => {
                  setShowSearch(false);
                  setSearchCategoryName("");
                }}
                cursor={"pointer"}
              >
                <IoIosClose fontSize={"24px"} />
              </InputRightAddon>
            </InputGroup>
            <Link to="/category-list/add-category">
              <Button
                variant={"outline"}
                colorScheme="teal"
                size={"sm"}
                py={5}
                px={4}
                ml={3}
                isDisabled={
                  !userData?.accessScopes?.category?.includes("write")
                }
              >
                Create Category
              </Button>
            </Link>
          </Flex>
        ) : (
          <Flex
            flexBasis={"100%"}
            justifyContent={"space-between"}
            alignItems={"center"}
          >
            <Breadcrumb flexBasis={"80%"} fontWeight="medium" fontSize="sm">
              <BreadcrumbItem>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbItem>

              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Categories</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
            <Text
              display={"flex"}
              px={4}
              py={"8.8px"}
              border={"1px"}
              borderColor={"gray.300"}
              rounded={"md"}
              color="gray.500"
              cursor={"pointer"}
              mb={0}
              onClick={() => setShowSearch(true)}
            >
              <IoMdSearch fontSize={"24px"} />
              <IoFilter fontSize={"22px"} ml={1} />
            </Text>
            <Link to="/category-list/add-category">
              <Button
                variant={"outline"}
                colorScheme="teal"
                size={"sm"}
                py={5}
                px={4}
                ml={3}
                isDisabled={
                  !userData?.accessScopes?.category?.includes("write")
                }
              >
                Create Category
              </Button>
            </Link>
          </Flex>
        )}
      </Flex>

      <Card>
        {!categories?.isLoading && categories?.error ? (
          <Flex
            justifyContent={"center"}
            alignItems={"center"}
            w={"full"}
            my={10}
          >
            <Text color={"red.500"}>
              Something went wrong please try again later...
            </Text>
          </Flex>
        ) : (
          <TableContainer
            height={`${window.innerHeight - 238}px`}
            overflowY={"scroll"}
          >
            <Table variant="simple">
              <Thead
                bgColor={"#c1eaee"}
                position={"sticky"}
                top={"0px"}
                zIndex={"99"}
              >
                <Tr bgColor={"#E2DFDF"}>
                  <Th>Name</Th>
                  <Th>Handle</Th>
                  <Th>Action</Th>
                </Tr>
              </Thead>
              <Tbody>
                {categories?.isLoading && !categories?.error ? (
                  <Tr>
                    <Td></Td>
                    <Td
                      display={"flex"}
                      justifyContent={"center"}
                      alignItems={"center"}
                    >
                      <Spinner />
                    </Td>
                    <Td></Td>
                  </Tr>
                ) : !categories?.isLoading && categories?.error ? (
                  <Flex
                    justifyContent={"center"}
                    alignItems={"center"}
                    w={"full"}
                    my={10}
                  >
                    <Text color={"red.500"}>
                      Something went wrong please try again later...
                    </Text>
                  </Flex>
                ) : !categories?.notFound ? (
                  categories?.result?.map((category, i) => {
                    return (
                      <Tr key={category._id}>
                        <Td fontSize={"14px"}>{category?.name || "n/a"}</Td>
                        <Td fontSize={"14px"}>{category?.handle || "n/a"}</Td>
                        <Td fontSize={"14px"}>
                          <Flex
                            justifyContent={"flex-start"}
                            alignItems={"center"}
                          >
                            {userData?.accessScopes?.category?.includes(
                              "read"
                            ) && (
                              <Tooltip label="View Category">
                                <Text
                                  as={"span"}
                                  fontSize={"18px"}
                                  cursor={"pointer"}
                                  onClick={() => {
                                    onOpen();
                                    setViewCategory({
                                      name: category?.name || "",
                                      des: category?.description || "",
                                      img: category?.image || "",
                                    });
                                  }}
                                >
                                  <MdRemoveRedEye />
                                </Text>
                              </Tooltip>
                            )}
                            {userData?.accessScopes?.category?.includes(
                              "write"
                            ) && (
                              <Tooltip label="Edit">
                                <Text
                                  as={"span"}
                                  ml={2}
                                  fontSize={"18px"}
                                  cursor={"pointer"}
                                  onClick={() =>
                                    navigate(
                                      `/category-list/edit-category/${category?._id}`
                                    )
                                  }
                                >
                                  <MdEdit />
                                </Text>
                              </Tooltip>
                            )}
                            {userData?.accessScopes?.category?.includes(
                              "delete"
                            ) && (
                              <Tooltip label="Delete">
                                <Text
                                  as={"span"}
                                  ml={2}
                                  fontSize={"18px"}
                                  cursor={"pointer"}
                                  onClick={() => {
                                    onOpen1();
                                    setDeleteCatId(category?._id);
                                  }}
                                >
                                  <MdDelete />
                                </Text>
                              </Tooltip>
                            )}
                          </Flex>
                        </Td>
                      </Tr>
                    );
                  })
                ) : (
                  <Tr>
                    <Td></Td>
                    <Td
                      display={"flex"}
                      justifyContent={"center"}
                      alignItems={"center"}
                    >
                      <Text color={"green.500"} fontWeight={"semibold"}>
                        No result found
                      </Text>
                    </Td>
                    <Td></Td>
                  </Tr>
                )}
              </Tbody>
            </Table>
          </TableContainer>
        )}
      </Card>

      {/* Pagination */}
      {!categories?.notFound && (
        <Flex
          justifyContent="center"
          alignItems="center"
          flexDirection={"row"}
          w={"100%"}
          mt={5}
        >
          <ReactPaginate
            previousLabel="Previous"
            nextLabel="Next"
            breakLabel="..."
            pageCount={totalPages}
            marginPagesDisplayed={2}
            pageRangeDisplayed={5}
            onPageChange={handlePageChange}
            containerClassName="pagination"
            activeClassName="active"
          />
        </Flex>
      )}
      {/* View Category */}
      <Modal isOpen={isOpen} onClose={onClose} size={"xl"}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Category Details</ModalHeader>
          <ModalCloseButton />
          <ModalBody bgColor={"#f2f2f2"}>
            <Card maxW="full" m={4}>
              <CardBody>
                <Flex justifyContent={"center"} alignItems={"center"}>
                  <Image
                    src={viewCategory?.img || ""}
                    alt={viewCategory?.name || "category-img-adim-khelSports"}
                    boxSize="150px"
                    borderRadius="full"
                    objectFit="cover"
                  />
                </Flex>
                <Stack mt="6" spacing="3">
                  <Flex justifyContent={"center"} alignItems={"center"}>
                    <Heading size="md">{viewCategory?.name || ""}</Heading>
                  </Flex>
                  <Text>{viewCategory?.des || ""}</Text>
                </Stack>
              </CardBody>
            </Card>
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* Delete ALert */}
      <AlertDialog
        motionPreset="slideInBottom"
        onClose={onClose1}
        isOpen={isOpen1}
        isCentered
      >
        <AlertDialogOverlay />

        <AlertDialogContent>
          <AlertDialogHeader>Delete Category</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            Are you sure you want to deleted this Category.
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button
              colorScheme="linkedin"
              onClick={() => {
                onClose1();
                setDeleteCatId("");
              }}
            >
              No
            </Button>
            <Button colorScheme="red" ml={3} onClick={deleteCategory}>
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
};

export default CategoryList;
