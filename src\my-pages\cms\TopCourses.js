import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import { Md<PERSON><PERSON><PERSON>, MdDragHandle } from "react-icons/md";
import {
  Box,
  BreadcrumbLink,
  Input,
  Breadcrumb,
  BreadcrumbItem,
  Card,
  CardBody,
  Button,
  Stack,
  Text,
  Flex,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  TableContainer,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  useToast,
  Spinner,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
  Badge,
  Tooltip,
} from "@chakra-ui/react";
import { Link } from "react-router-dom";
import axios from "axios";
import { useSelector } from "react-redux";

const TopCourses = () => {
  const [courseData, setCourseData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [cmsCourseData, setCmsCourseData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });

  const [isUpdated, setIsUpdated] = useState(false);
  const [saveChangesBtnLoading, setSaveChangesBtnLoading] = useState(false);
  const [draggedItemIndex, setDraggedItemIndex] = useState(null);
  const [adjustBtnEdit, setAdjustBtnEdit] = useState(false);
  const [prevCourseData, setPrevCourseData] = useState([]);
  const [posBtnLoading, setPosBtnLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isDeleted, setIsDeleted] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [isOpen1, setIsOpen1] = useState(false);
  const onClose1 = () => setIsOpen1(false);
  const onOpen1 = () => setIsOpen1(true);

  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getCourseData = (searchQuery) => {
    setCourseData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/course/filter?${
        searchQuery.length > 0 ? "search=" + searchQuery : "search="
      }&page=1`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        console.log(response,"opopopop")
        setCourseData({
          result: response.data,
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        setCourseData({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const getCmsCourseData = () => {
    setCmsCourseData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/course`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    axios
      .request(config)
      .then((response) => {
        setCmsCourseData({
          result: response.data.sort((a, b) => a.position - b.position),
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        setCmsCourseData({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const updateAndSave = () => {
    onClose1();
    setSaveChangesBtnLoading(true);
    const getIds = cmsCourseData.result.map((id, index) => {
      return { course: id.course, position: index + 1 };
    });

    let data = JSON.stringify({
      documents: getIds,
    });
    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/course/add`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        getCmsCourseData();
        getCourseData(searchQuery);
        setSaveChangesBtnLoading(false);
        setIsUpdated(false);
        onClose();
        setIsDeleted(false);
        toast({
          title: "Course updated successfully",
          status: "success",
          duration: 3500,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setSaveChangesBtnLoading(false);
        if (
          error.response.data.message === "Top Course cannot be greater than 15"
        ) {
          getCmsCourseData();
          setIsUpdated(false);
          toast({
            title: error.response.data.message,
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handleDragStart = (index) => {
    setDraggedItemIndex(index);
  };

  const handleDragOver = (index) => {
    if (draggedItemIndex === null) return;
    const newItems = [...cmsCourseData.result]; // Copy the result array from blockData
    const draggedItem = newItems[draggedItemIndex];
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);

    // Adjust positions of items inside newItems array
    newItems.forEach((item, idx) => {
      item.position = idx + 1;
    });

    setCmsCourseData({ ...cmsCourseData, result: newItems }); // Update result array in blockData
    setDraggedItemIndex(index);
  };

  const handleDragEnd = () => {
    setDraggedItemIndex(null);
  };

  const updateBlockPosition = () => {
    setPosBtnLoading(true);
    const value = cmsCourseData.result.map((x) => {
      return { id: x._id, newPosition: x.position };
    });
    let data = JSON.stringify({
      updates: value,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/course/position`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setPosBtnLoading(false);
        setPrevCourseData([]);
        setAdjustBtnEdit(false);
        toast({
          title: "Block position updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        onClose1();
        setPosBtnLoading(false);
        setPrevCourseData([]);
        setAdjustBtnEdit(false);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  useEffect(() => {
    getCmsCourseData();
  }, []);

  return (
    <Layout title="CMS | Top Courses" content="container">
      <Flex justifyContent={"space-between"} alignItems={"center"}>
        <Breadcrumb fontWeight="medium" fontSize="sm">
          <BreadcrumbItem>
            <Link to={"/"}>Dashboard</Link>
          </BreadcrumbItem>

          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink>CMS</BreadcrumbLink>
          </BreadcrumbItem>

          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink href="#">Top Course</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>
        {userData?.accessScopes?.cms?.includes("write") && (
          <>
            {isDeleted ? (
              !(!cmsCourseData?.isLoading && cmsCourseData?.error) &&
              !adjustBtnEdit &&
              userData?.accessScopes?.cms?.includes("write") && (
                <Flex justifyContent={"flex-end"} alignItems={"center"}>
                  <Button
                    variant={"outline"}
                    colorScheme="red"
                    size={"sm"}
                    py={5}
                    px={4}
                    mr={4}
                    isDisabled={!isUpdated}
                    onClick={() => {
                      setIsDeleted(false);
                      getCmsCourseData();
                      setIsUpdated(false);
                    }}
                  >
                    Discard
                  </Button>
                  <Button
                    variant={"outline"}
                    colorScheme="green"
                    size={"sm"}
                    py={5}
                    px={4}
                    isDisabled={!isUpdated}
                    onClick={onOpen1}
                    isLoading={saveChangesBtnLoading}
                  >
                    Save Changes
                  </Button>
                </Flex>
              )
            ) : (
              <Flex>
                {!adjustBtnEdit ? (
                  <Box>
                    <Button
                      variant={"outline"}
                      colorScheme="telegram"
                      size={"sm"}
                      py={5}
                      px={4}
                      mr={3}
                      onClick={() => {
                        setAdjustBtnEdit(true);
                        setPrevCourseData(cmsCourseData.result);
                        getCmsCourseData();
                        setIsUpdated(false);
                      }}
                    >
                      Adjust Position
                    </Button>
                    <Button
                      variant={"outline"}
                      colorScheme="teal"
                      size={"sm"}
                      py={5}
                      px={4}
                      onClick={() => {
                        onOpen();
                        setSearchQuery("");
                        getCourseData("");
                      }}
                      mr={2}
                      isDisabled={
                        !cmsCourseData?.isLoading && cmsCourseData?.error
                      }
                    >
                      Add Course
                    </Button>
                  </Box>
                ) : (
                  <Flex>
                    <Button
                      variant={"outline"}
                      colorScheme="red"
                      size={"sm"}
                      py={5}
                      px={4}
                      mr={4}
                      onClick={() => {
                        setCmsCourseData({
                          result: prevCourseData,
                          isLoading: false,
                          error: false,
                        });
                        setAdjustBtnEdit(false);
                        getCmsCourseData();
                        setIsUpdated(false);
                      }}
                    >
                      Discard
                    </Button>
                    <Button
                      variant={"outline"}
                      colorScheme="green"
                      size={"sm"}
                      py={5}
                      px={4}
                      isLoading={posBtnLoading}
                      onClick={updateBlockPosition}
                    >
                      Save Changes
                    </Button>
                  </Flex>
                )}
              </Flex>
            )}
          </>
        )}
      </Flex>
      {/* Added/Selected Course List */}
      {!cmsCourseData?.isLoading && cmsCourseData?.error ? (
        <Flex
          justifyContent={"center"}
          alignItems={"center"}
          w={"full"}
          my={10}
        >
          <Text color={"red.500"}>
            Something went wrong please try again later...
          </Text>
        </Flex>
      ) : (
        <TableContainer
          mt={6}
          height={`${window.innerHeight - 200}px`}
          overflowY={"scroll"}
        >
          <Table variant="simple">
            <Thead
              bgColor={"#c1eaee"}
              position={"sticky"}
              top={"0px"}
              zIndex={"99"}
            >
              <Tr bgColor={"#E2DFDF"}>
                <Th>Position</Th>
                <Th>Course Name</Th>
                <Th>Coach Name</Th>
                <Th>Status</Th>
                <Th>Category</Th>
                <Th>Proficiency</Th>
                <Th>Enrolled</Th>
                <Th textAlign={"center"}>Action</Th>
              </Tr>
            </Thead>
            <Tbody>
              {cmsCourseData?.isLoading && !cmsCourseData?.error ? (
                <Tr>
                  <Td></Td>
                  <Td></Td>
                  <Td> </Td>
                  <Td
                    display={"flex"}
                    justifyContent={"flex-end"}
                    alignItems={"center"}
                  >
                    <Spinner />
                  </Td>
                  <Td></Td>
                  <Td></Td>
                  <Td></Td>
                  <Td></Td>
                </Tr>
              ) : (
                cmsCourseData?.result?.map((courseData, inx) => {
                  return (
                    <>
                      {adjustBtnEdit ? (
                        <Tr
                          key={inx}
                          draggable
                          onDragStart={() => handleDragStart(inx)}
                          onDragOver={() => handleDragOver(inx)}
                          onDragEnd={handleDragEnd}
                        >
                          <Td fontSize={"14px"}>{inx + 1 + "."}</Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {courseData?.course?.courseName}
                          </Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {courseData?.course?.coachName}
                          </Td>
                          <Td fontSize={"14px"}>
                            <Badge
                              colorScheme={
                                courseData?.course?.status === "active"
                                  ? "green"
                                  : "red"
                              }
                            >
                              {courseData?.course?.status?.toUpperCase()}
                            </Badge>
                          </Td>
                          <Td fontSize={"14px"}>
                            {courseData?.course?.category}
                          </Td>
                          <Td fontSize={"14px"}>
                            {courseData?.course?.proficiency[0]
                              ?.charAt(0)
                              .toUpperCase() +
                              courseData?.course?.proficiency[0]?.slice(1)}
                          </Td>
                          <Td fontSize={"14px"}>
                            {courseData?.course?.playerEnrolled}
                          </Td>
                          <Td fontSize={"14px"}>
                            {!adjustBtnEdit ? (
                              userData?.accessScopes?.cms?.includes(
                                "delete"
                              ) && (
                                <Flex
                                  justifyContent={"center"}
                                  alignItems={"center"}
                                  cursor={"pointer"}
                                  onClick={() => {
                                    setIsUpdated(true);
                                    setCmsCourseData((prevState) => ({
                                      ...prevState,
                                      result: prevState?.result.filter(
                                        (obj) =>
                                          obj?.course?._id !==
                                          courseData?.course?._id
                                      ),
                                    }));
                                  }}
                                >
                                  <Tooltip label="Delete Coruse">
                                    <Text>
                                      <MdDelete fontSize={"24px"} />
                                    </Text>
                                  </Tooltip>
                                </Flex>
                              )
                            ) : (
                              <Flex
                                justifyContent={"center"}
                                alignItems={"center"}
                              >
                                <Text as={"span"} ml={3}>
                                  <MdDragHandle
                                    style={{ cursor: "grab" }}
                                    fontSize={"24px"}
                                  />
                                </Text>
                              </Flex>
                            )}
                          </Td>
                        </Tr>
                      ) : (
                        <Tr key={inx}>
                          <Td fontSize={"14px"}>{inx + 1 + "."}</Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {courseData?.course?.courseName}
                          </Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {courseData?.course?.coachName}
                          </Td>
                          <Td fontSize={"14px"}>
                            <Badge
                              colorScheme={
                                courseData?.course?.status === "active"
                                  ? "green"
                                  : "red"
                              }
                            >
                              {courseData?.course?.status?.toUpperCase()}
                            </Badge>
                          </Td>
                          <Td fontSize={"14px"}>
                            {courseData?.course?.category}
                          </Td>
                          <Td fontSize={"14px"}>
                            {courseData?.course?.proficiency[0]
                              ?.charAt(0)
                              .toUpperCase() +
                              courseData?.course?.proficiency[0]?.slice(1)}
                          </Td>
                          <Td fontSize={"14px"}>
                            {courseData?.course?.playerEnrolled}
                          </Td>
                          <Td fontSize={"14px"}>
                            {!adjustBtnEdit ? (
                              userData?.accessScopes?.cms?.includes(
                                "delete"
                              ) && (
                                <Flex
                                  justifyContent={"center"}
                                  alignItems={"center"}
                                  cursor={"pointer"}
                                  onClick={() => {
                                    setIsUpdated(true);
                                    setIsDeleted(true);
                                    setCmsCourseData((prevState) => ({
                                      ...prevState,
                                      result: prevState?.result.filter(
                                        (obj) =>
                                          obj?.course?._id !==
                                          courseData?.course?._id
                                      ),
                                    }));
                                  }}
                                >
                                  <Tooltip label="Delete Coruse">
                                    <Text>
                                      <MdDelete fontSize={"24px"} />
                                    </Text>
                                  </Tooltip>
                                </Flex>
                              )
                            ) : (
                              <Flex
                                justifyContent={"center"}
                                alignItems={"center"}
                              >
                                <Text as={"span"} ml={3}>
                                  <MdDragHandle
                                    style={{ cursor: "grab" }}
                                    fontSize={"24px"}
                                  />
                                </Text>
                              </Flex>
                            )}
                          </Td>
                        </Tr>
                      )}
                    </>
                  );
                })
              )}
            </Tbody>
          </Table>
        </TableContainer>
      )}
      {/* Modal for course search */}
      <Modal
        isOpen={isOpen}
        onClose={() => {
          onClose();
          getCmsCourseData();
          setIsUpdated(false);
        }}
        size="6xl"
      >
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <Flex justifyContent={"space-between"} alignItems={"center"}>
              <Text mb={0}>Search Course</Text>
              <Flex>
                {!(!cmsCourseData?.isLoading && cmsCourseData?.error) &&
                  !adjustBtnEdit &&
                  userData?.accessScopes?.cms?.includes("write") && (
                    <Flex
                      justifyContent={"flex-end"}
                      alignItems={"center"}
                      mr={7}
                    >
                      <Button
                        variant={"outline"}
                        colorScheme="red"
                        size={"sm"}
                        py={3}
                        px={4}
                        mr={2}
                        isDisabled={!isUpdated}
                        onClick={() => {
                          getCmsCourseData();
                          setIsUpdated(false);
                          onClose();
                        }}
                      >
                        Discard
                      </Button>
                      <Button
                        variant={"outline"}
                        colorScheme="green"
                        size={"sm"}
                        py={3}
                        px={4}
                        isDisabled={!isUpdated}
                        onClick={onOpen1}
                        isLoading={saveChangesBtnLoading}
                      >
                        Save Changes
                      </Button>
                    </Flex>
                  )}
                <ModalCloseButton />
              </Flex>
            </Flex>
          </ModalHeader>
          <ModalBody>
            <Card mt={3} variant="outline" height={"95%"}>
              <CardBody>
                <Box>
                  <Stack direction="row" spacing={4} align="center">
                    <Input
                      type="text"
                      placeholder="Search Coach"
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={(e) =>
                        e.key === "Enter" && getCourseData(searchQuery)
                      }
                    />
                    <Button
                      variant="solid"
                      colorScheme="telegram"
                      onClick={() => getCourseData(searchQuery)}
                      isDisabled={!(searchQuery.length >= 3)}
                    >
                      Search
                    </Button>
                  </Stack>
                  {/* Searched Course List */}
                  {!courseData?.isLoading && courseData?.error ? (
                    <Flex
                      justifyContent={"center"}
                      alignItems={"center"}
                      w={"full"}
                      my={10}
                    >
                      <Text color={"red.500"}>
                        Something went wrong please try again later...
                      </Text>
                    </Flex>
                  ) : courseData.result.length > 0 ? (
                    <TableContainer
                      mt={6}
                      height={`${window.innerHeight - 300}px`}
                      overflowY={"scroll"}
                    >
                      <Table variant="simple">
                        <Thead
                          bgColor={"#c1eaee"}
                          position={"sticky"}
                          top={"0px"}
                          zIndex={"99"}
                        >
                          <Tr bgColor={"#E2DFDF"}>
                            <Th>S.No</Th>
                            <Th>Course Name</Th>
                            <Th>Coach Name</Th>
                            <Th>Status</Th>
                            <Th>Category</Th>
                            <Th>Proficiency</Th>
                            <Th>Enrolled</Th>
                            <Th textAlign={"center"}>Action</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {courseData?.isLoading && !courseData?.error ? (
                            <Tr>
                              <Td></Td>
                              <Td></Td>
                              <Td> </Td>
                              <Td
                                display={"flex"}
                                justifyContent={"flex-end"}
                                alignItems={"center"}
                              >
                                <Spinner />
                              </Td>
                              <Td></Td>
                              <Td></Td>
                              <Td></Td>
                              <Td></Td>
                            </Tr>
                          ) : (
                            courseData?.result?.map((courseData, inx) => {
                              return (
                                <Tr key={inx}>
                                  <Td>{inx + 1 + "."}</Td>
                                  <Td
                                    style={{
                                      whiteSpace: "pre-wrap",
                                      wordWrap: "break-word",
                                    }}
                                  >
                                    {courseData?.courseName}
                                  </Td>
                                  <Td
                                    style={{
                                      whiteSpace: "pre-wrap",
                                      wordWrap: "break-word",
                                    }}
                                  >
                                    {courseData?.coachName}
                                  </Td>
                                  <Td>
                                    <Badge
                                      colorScheme={
                                        courseData?.status === "active"
                                          ? "green"
                                          : "red"
                                      }
                                    >
                                      {courseData?.status?.toUpperCase()}
                                    </Badge>
                                  </Td>
                                  <Td>{courseData?.category}</Td>
                                  <Td>
                                    {courseData?.proficiency[0]
                                      ?.charAt(0)
                                      .toUpperCase() +
                                      courseData?.proficiency[0]?.slice(1)}
                                  </Td>
                                  <Td>{courseData?.playerEnrolled || "n/a"}</Td>

                                  <Td>
                                    {cmsCourseData?.result.every(
                                      (z) => z?.course?._id !== courseData?._id
                                    ) ? (
                                      <Button
                                        colorScheme="telegram"
                                        size={"sm"}
                                        onClick={() => {
                                          setIsUpdated(true);
                                          setCmsCourseData((prevState) => ({
                                            ...prevState,
                                            result: [
                                              ...prevState.result,
                                              { course: courseData },
                                            ],
                                          }));
                                        }}
                                      >
                                        Add
                                      </Button>
                                    ) : (
                                      <Button
                                        colorScheme="red"
                                        size={"sm"}
                                        onClick={() => {
                                          setIsUpdated(true);
                                          setCmsCourseData((prevState) => ({
                                            ...prevState,
                                            result: prevState.result.filter(
                                              (obj) =>
                                                obj?.course?._id !==
                                                courseData?._id
                                            ),
                                          }));
                                        }}
                                      >
                                        Remove
                                      </Button>
                                    )}
                                  </Td>
                                </Tr>
                              );
                            })
                          )}
                        </Tbody>
                      </Table>
                    </TableContainer>
                  ) : courseData?.isLoading && !courseData?.error ? (
                    <Flex
                      justifyContent={"center"}
                      alignItems={"center"}
                      mt={6}
                    >
                      <Spinner size={"lg"} />
                    </Flex>
                  ) : (
                    <Flex
                      justifyContent={"center"}
                      alignItems={"center"}
                      mt={6}
                    >
                      <Text
                        fontSize={"18px"}
                        fontWeight={"semibold"}
                        color={"red.300"}
                      >
                        Result not found
                      </Text>
                    </Flex>
                  )}
                </Box>
              </CardBody>
            </Card>
          </ModalBody>
        </ModalContent>
      </Modal>
      {/* Save Changes Alert */}
      <AlertDialog
        motionPreset="slideInBottom"
        onClose={onClose1}
        isOpen={isOpen1}
        isCentered
      >
        <AlertDialogOverlay />

        <AlertDialogContent>
          <AlertDialogHeader>Save Changes</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            Are you sure you want to make these changes.
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button onClick={onClose1}>No</Button>
            <Button
              colorScheme="telegram"
              ml={3}
              onClick={() => updateAndSave()}
            >
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
};

export default TopCourses;
