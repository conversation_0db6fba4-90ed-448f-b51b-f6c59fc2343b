import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import {
  Accordion,
  AccordionButton,
  AccordionItem,
  AccordionPanel,
  AlertDialog,
  AlertDialogBody,
  AlertDialogCloseButton,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Card,
  CardBody,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Input,
  Link,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalFooter,
  ModalHeader,
  ModalOverlay,
  Text,
  Textarea,
  Tooltip,
  useDisclosure,
  useToast,
} from "@chakra-ui/react";
import { FaMinus } from "react-icons/fa";
import { IoMdAdd } from "react-icons/io";
import axios from "axios";
import { MdEdit, MdDelete } from "react-icons/md";
import { getCookie } from "../../utilities/auth";
import { useSelector } from "react-redux";

const FaqCms = () => {
  const [faqData, setFaqData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [isEditable, setIsEditable] = useState(false);
  const [editOptions, setEditOptions] = useState(false);
  const [selectedEditData, setSelectedEditData] = useState({
    question: "",
    answer: "",
    id: "",
  });
  const [addFaq, setAddFaq] = useState({ question: "", answer: "" });
  const [renderOnAdd, setRenderOnAdd] = useState(0);
  const [isSelectedEditData, setIsSelectedEditData] = useState(false);
  const [isSelectedEditDataAnswer, setIsSelectedEditDataAnswer] =
    useState(false);
  const [isSelectedEditDataQuestion, setIsSelectedEditDataQuestion] =
    useState(false);
  const [addBtnLoading, setAddBtnLoading] = useState(false);
  const [deleteBtnLoading, setDeleteBtnLoading] = useState(false);
  const [saveDeleteId, setSaveDeleteId] = useState("");
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [isOpen1, setIsOpen1] = useState(false);
  const onClose1 = () => setIsOpen1(false);
  const onOpen1 = () => setIsOpen1(true);

  const [isOpen2, setIsOpen2] = useState(false);
  const onClose2 = () => setIsOpen2(false);
  const onOpen2 = () => setIsOpen2(true);

  const Toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getFaqData = () => {
    setFaqData({ result: [], isLoading: true, error: false });

    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/faq`,
      headers: {},
    };
    axios
      .request(config)
      .then((response) => {
        setFaqData({ result: response.data, isLoading: false, error: false });
      })
      .catch((error) => {
        console.log(error);
        setFaqData({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          Toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          Toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const addDataHandler = () => {
    setAddBtnLoading(true);
    let data = JSON.stringify({
      question: `${addFaq.question}`,
      answer: `${addFaq.answer}`,
    });

    if (!(addFaq.question.length >= 3)) {
      setAddBtnLoading(false);
      Toast({
        title: "Please add atleast 3 words in question field",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else if (!(addFaq.answer.length >= 3)) {
      setAddBtnLoading(false);
      Toast({
        title: "Please add atleast 3 words in answer field",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else {
      let config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/cms/create/faq`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {
          setAddFaq({ question: "", answer: "" });
          setIsSelectedEditDataAnswer(false);
          setIsSelectedEditDataQuestion(false);
          setAddBtnLoading(false);
          onClose1();
          setRenderOnAdd((prev) => prev + 1);
          Toast({
            title: "FAQ",
            description: "Successfully Added...",
            status: "success",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log(error);
          setIsSelectedEditDataAnswer(false);
          setIsSelectedEditDataQuestion(false);
          setAddBtnLoading(false);
          onClose1();
          if (error.response.status === 403) {
            Toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            Toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    }
  };

  const deleteDataHandler = () => {
    setDeleteBtnLoading(true);
    setIsEditable(false);
    setEditOptions(false);
    let config = {
      method: "delete",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/delete/faq/${saveDeleteId}`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setDeleteBtnLoading(false);
        setSaveDeleteId("");
        onClose2();
        Toast({
          title: "FAQ",
          description: "Successfully Deleted...",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        setRenderOnAdd((prev) => prev + 1);
      })
      .catch((error) => {
        console.log(error);
        setSaveDeleteId("");
        setDeleteBtnLoading(false);
        onClose2();
        if (error.response.status === 403) {
          Toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          Toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const updateHandler = () => {
    let data = JSON.stringify({
      question: `${selectedEditData.question}`,
      answer: `${selectedEditData.answer}`,
    });
    if (!(selectedEditData.question.length >= 3)) {
      setAddBtnLoading(false);
      Toast({
        title: "Please add atleast 3 words in question field",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else if (!(selectedEditData.question.length >= 3)) {
      setAddBtnLoading(false);
      Toast({
        title: "Please add atleast 3 words in answer field",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else {
      let config = {
        method: "patch",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/cms/update/faq/${selectedEditData.id}`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {
          setAddFaq({ question: "", answer: "" });
          setIsSelectedEditData(false);
          setAddBtnLoading(false);
          onClose();
          setRenderOnAdd((prev) => prev + 1);
          setSelectedEditData({
            question: "",
            answer: "",
            id: "",
          });
          Toast({
            title: "FAQ",
            description: "Successfully updated...",
            status: "success",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log(error);
          onClose();
          setSelectedEditData({
            question: "",
            answer: "",
            id: "",
          });
          if (error.response.status === 403) {
            Toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            Toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
          setAddFaq({ question: "", answer: "" });
          setIsSelectedEditData(false);
          setAddBtnLoading(false);
        });
    }
  };

  useEffect(() => {
    getFaqData();
  }, [renderOnAdd]);

  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="CMS | FAQ" content="container">
        <Flex justifyContent={"space-between"} alignItems={"center"} mb={6}>
          <Breadcrumb fontWeight="medium" fontSize="sm">
            <BreadcrumbItem>
              <BreadcrumbLink>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>

            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink>CMS</BreadcrumbLink>
            </BreadcrumbItem>

            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink href="#">FAQ</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>

          <Flex>
            {isEditable ? (
              <Button
                variant={"outline"}
                colorScheme="red"
                size={"sm"}
                py={5}
                px={4}
                mr={3}
                isDisabled={faqData?.result?.length == 0}
                onClick={() => {
                  setIsEditable(false);
                  setEditOptions(false);
                  setIsSelectedEditData(false);
                  setSelectedEditData({ question: "", answer: "", id: "" });
                }}
              >
                Cancel Edit Option
              </Button>
            ) : (
              (userData?.accessScopes?.cms?.includes("write") ||
                userData?.accessScopes?.cms?.includes("delete")) && (
                <Button
                  variant={"outline"}
                  colorScheme="teal"
                  size={"sm"}
                  py={5}
                  px={4}
                  isDisabled={faqData?.result?.length == 0}
                  mr={3}
                  onClick={() => {
                    setIsEditable(true);
                    setEditOptions(true);
                    setIsSelectedEditData(false);
                    setIsSelectedEditDataAnswer(true);
                    setIsSelectedEditDataQuestion(true);
                    setSelectedEditData({ question: "", answer: "", id: "" });
                  }}
                >
                  Edit
                </Button>
              )
            )}
            {userData?.accessScopes?.cms?.includes("write") && (
              <Button
                variant={"outline"}
                colorScheme="teal"
                size={"sm"}
                py={5}
                px={4}
                isDisabled={isEditable || editOptions}
                onClick={() => {
                  onOpen1();
                  setIsSelectedEditDataAnswer(false);
                  setIsSelectedEditDataQuestion(false);
                  setAddFaq({ question: "", answer: "" });
                }}
              >
                Add
              </Button>
            )}
          </Flex>
        </Flex>
        <Card mt={4}>
          <CardBody>
            <Heading as="h4" size="md" mb={6}>
              FAQ
            </Heading>
            <Accordion allowMultiple>
              {faqData.result.map((faq, i) => {
                return (
                  <AccordionItem key={i}>
                    {({ isExpanded }) => (
                      <>
                        <Flex justifyContent={"center"} alignItems={"center"}>
                          <AccordionButton
                            pointerEvents={editOptions && "none"}
                          >
                            <Box as="span" flex="1" textAlign="left">
                              {faq.question}
                            </Box>
                            {!editOptions &&
                              (isExpanded ? <FaMinus /> : <IoMdAdd />)}
                          </AccordionButton>
                          {editOptions && (
                            <Flex
                              justifyContent={"space-between"}
                              alignItems={"center"}
                            >
                              {userData?.accessScopes?.cms?.includes(
                                "write"
                              ) && (
                                <Text
                                  as="span"
                                  cursor={"pointer"}
                                  fontSize={"20px"}
                                  onClick={() => {
                                    setSelectedEditData({
                                      question: faq.question,
                                      answer: faq.answer,
                                      id: faq._id,
                                    });
                                    onOpen();
                                    setIsSelectedEditData(false);
                                  }}
                                >
                                  <MdEdit />
                                </Text>
                              )}
                              {userData?.accessScopes?.cms?.includes(
                                "delete"
                              ) && (
                                <Text
                                  as="span"
                                  cursor={"pointer"}
                                  fontSize={"20px"}
                                  onClick={() => {
                                    onOpen2();
                                    setSaveDeleteId(faq._id);
                                  }}
                                  ml={2}
                                >
                                  <MdDelete />
                                </Text>
                              )}
                            </Flex>
                          )}
                        </Flex>
                        <AccordionPanel pb={4}>{faq.answer}</AccordionPanel>
                      </>
                    )}
                  </AccordionItem>
                );
              })}
            </Accordion>
          </CardBody>
        </Card>
        {/* Edit Modal */}
        <Modal isOpen={isOpen} onClose={onClose} size={"xl"}>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>FAQ</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <Text mb={1} fontSize={"18px"} fontWeight={"semibold"}>
                Question
              </Text>
              <Input
                mb={4}
                value={selectedEditData.question}
                placeholder="Enter FAQ Question"
                onChange={(e) => {
                  setSelectedEditData((prevData) => ({
                    ...prevData,
                    question: e.target.value,
                  }));
                }}
              />
              <Text mb={1} fontSize={"18px"} fontWeight={"semibold"}>
                Answer
              </Text>
              <Textarea
                value={selectedEditData.answer}
                placeholder="Enter FAQ Answer"
                onChange={(e) => {
                  setSelectedEditData((prevData) => ({
                    ...prevData,
                    answer: e.target.value,
                  }));
                }}
              />
            </ModalBody>
            <ModalFooter>
              <Button
                colorScheme="red"
                variant={"outline"}
                colorScheme="red"
                size={"sm"}
                py={5}
                px={4}
                mr={3}
                onClick={onClose}
              >
                Discard
              </Button>
              <Button
                variant={"outline"}
                colorScheme="green"
                size={"sm"}
                py={5}
                px={4}
                onClick={updateHandler}
                isLoading={addBtnLoading}
              >
                Save
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
        {/* Add Modal */}
        <Modal isOpen={isOpen1} onClose={onClose1} size={"xl"}>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>Add FAQ</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <FormControl>
                <FormLabel>Question</FormLabel>
                <Input
                  value={addFaq.question}
                  placeholder="Enter FAQ Question"
                  onChange={(e) => {
                    if (e.target.value.length >= 3) {
                      setIsSelectedEditDataQuestion(true);
                    } else {
                      setIsSelectedEditDataQuestion(false);
                    }
                    setAddFaq((prev) => ({
                      ...prev,
                      question: e.target.value,
                    }));
                  }}
                />
              </FormControl>
              <FormControl mt={4}>
                <FormLabel>Answer</FormLabel>
                <Textarea
                  value={addFaq.answer}
                  placeholder="Enter FAQ Answer"
                  onChange={(e) => {
                    setAddFaq((prev) => ({ ...prev, answer: e.target.value }));
                  }}
                />
              </FormControl>
            </ModalBody>
            <ModalFooter>
              <Button
                variant={"outline"}
                colorScheme="red"
                size={"sm"}
                py={5}
                px={4}
                mr={3}
                onClick={onClose1}
              >
                Discard
              </Button>
              <Button
                variant={"outline"}
                colorScheme="green"
                size={"sm"}
                py={5}
                px={4}
                // isDisabled={
                //   !isSelectedEditData ||
                //   addFaq.question.length === 0 ||
                //   addFaq.answer.length === 0
                // }
                onClick={addDataHandler}
                isLoading={addBtnLoading}
              >
                Save
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
        {/* Delete ALert */}
        <AlertDialog
          motionPreset="slideInBottom"
          onClose={onClose2}
          isOpen={isOpen2}
          isCentered
        >
          <AlertDialogOverlay />

          <AlertDialogContent>
            <AlertDialogHeader>Delete FAQ</AlertDialogHeader>
            <AlertDialogCloseButton />
            <AlertDialogBody>
              Are you sure you want to deleted this FAQ.
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button
                colorScheme="linkedin"
                onClick={() => {
                  setSaveDeleteId("");
                  onClose2();
                }}
                isLoading={deleteBtnLoading}
              >
                No
              </Button>
              <Button colorScheme="red" ml={3} onClick={deleteDataHandler}>
                Yes
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </Layout>
    </Box>
  );
};

export default FaqCms;
