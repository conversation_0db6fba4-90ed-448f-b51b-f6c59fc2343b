import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Card,
  CardBody,
  Flex,
  Heading,
  Link,
  Spinner,
  Text,
  useToast,
} from "@chakra-ui/react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import axios from "axios";
import { useSelector } from "react-redux";

const RegistrationDetailPageCms = () => {
  const [registrationDetailData, setRegistrationDetailData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [isUpdated, setIsUpdated] = useState(false);
  const [discardBtnLoading, setDiscardBtnLoading] = useState(false);
  const [saveBtnLoading, setSaveBtnLoading] = useState(false);

  const Toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getRegistrationData = () => {
    setRegistrationDetailData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/cms-registration-details`,
      headers: {},
    };

    axios
      .request(config)
      .then((response) => {
        setRegistrationDetailData({
          result: response.data,
          isLoading: false,
          error: false,
        });
        setDiscardBtnLoading(false);
        setIsUpdated(false);
      })
      .catch((error) => {
        console.log(error);
        setRegistrationDetailData({
          result: [],
          isLoading: false,
          error: true,
        });
        setDiscardBtnLoading(false);
        setIsUpdated(false);
        if (error.response.status === 403) {
          Toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          Toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const updateRegistrationData = (id) => {
    let data = JSON.stringify({
      registrationData: `${registrationDetailData.result[0].registrationData}`,
    });

    if (registrationDetailData.result[0].registrationData.length === 36) {
      setSaveBtnLoading(false);
      Toast({
        title: "Please add atleast 10 words in about us",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else {
      let config = {
        method: "patch",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/cms/update/cms-registration-details/${id}`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {

          setSaveBtnLoading(false);
          setIsUpdated(false);
          Toast({
            title: "Registration Details",
            description: "Successfully updated...",
            status: "success",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log(error);
          setSaveBtnLoading(false);
          if (error.response.status === 403) {
            Toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            Toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    }
  };

  useEffect(() => {
    getRegistrationData();
  }, []);

  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="CMS | About Us" content="container">
        <Flex justifyContent={"space-between"} alignItems={"center"}>
          <Breadcrumb fontWeight="medium" fontSize="sm">
            <BreadcrumbItem>
              <BreadcrumbLink>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>

            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink>CMS</BreadcrumbLink>
            </BreadcrumbItem>

            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink href="#">Registration Detail Page</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
          {userData?.accessScopes?.cms?.includes("write") && (
            <Box>
              <Button
                variant={"outline"}
                colorScheme="red"
                size={"sm"}
                py={5}
                px={4}
                mr={2}
                isDisabled={!isUpdated}
                isLoading={discardBtnLoading}
                onClick={() => {
                  setDiscardBtnLoading(true);
                  getRegistrationData();
                }}
              >
                Discard
              </Button>
              <Button
                variant={"outline"}
                colorScheme="green"
                size={"sm"}
                py={5}
                px={4}
                isDisabled={!isUpdated}
                isLoading={saveBtnLoading}
                onClick={() => {
                  setSaveBtnLoading(true);
                  updateRegistrationData(registrationDetailData.result[0]._id);
                }}
              >
                Save Changes
              </Button>
            </Box>
          )}
        </Flex>
        {registrationDetailData.isLoading ? (
          <Flex
            w={"full"}
            justifyContent={"center"}
            alignItems={"center"}
            mt={12}
          >
            <Spinner size={"lg"} />
          </Flex>
        ) : !registrationDetailData.isLoading &&
          !registrationDetailData.error ? (
          <>
            <Card mt={4}>
              <CardBody>
                <Heading as="h4" size="md" mb={3}>
                  Registration Details
                </Heading>
                {userData?.accessScopes?.cms?.includes("write") ? (
                  <ReactQuill
                    theme="snow"
                    value={
                      registrationDetailData?.result[0]?.registrationData || ""
                    }
                    onChange={(e) => {
                      setRegistrationDetailData((prev) => ({
                        ...prev,
                        result: [{ ...prev.result[0], registrationData: e }],
                      }));
                      setIsUpdated(true);
                    }}
                  />
                ) : (
                  <ReactQuill
                    theme="snow"
                    value={
                      registrationDetailData?.result[0]?.registrationData || ""
                    }
                    readOnly
                    onChange={(e) => {
                      setRegistrationDetailData((prev) => ({
                        ...prev,
                        result: [{ ...prev.result[0], registrationData: e }],
                      }));
                      setIsUpdated(true);
                    }}
                  />
                )}
              </CardBody>
            </Card>
          </>
        ) : (
          <Flex
            w={"full"}
            justifyContent={"center"}
            alignItems={"center"}
            mt={12}
          >
            <Text color={"red.500"}>
              Something went wrong, please try again later...
            </Text>
          </Flex>
        )}
      </Layout>
    </Box>
  );
};

export default RegistrationDetailPageCms;
