import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>og,
  AlertDialogBody,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  Button,
  Spinner,
  Input,
  FormControl,
  FormLabel,
} from "@chakra-ui/react";

const ConfirmationModal = (props) => {
  // isOpen, onClose,heading,action,name,onCLickfunctiona

  const cancelRef = React.useRef();
  return (
    <>
      <AlertDialog
        isOpen={props.isOpen}
        leastDestructiveRef={cancelRef}
        onClose={props.onClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              {props.heading}
            </AlertDialogHeader>

            <AlertDialogBody>
              {props.action} {props.name}
            </AlertDialogBody>
            {props.reason && (
              <FormControl width="80%" margin="auto" mt="10px">
                <FormLabel margin="auto">Reason For Edit</FormLabel>
                <Input
                  placeholder="write Reason For Edit"
                  onChange={(e) => props.setReasonInput(e.target.value)}
                />
              </FormControl>
            )}

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={props.onClose}>
                Cancel
              </Button>
              {props.loader ? (
                <Button
                  colorScheme={props.color ? props.color : "red"}
                  onClick={props.onClickFunction}
                  ml={3}
                  isDisabled={props.reason && !props.reasonInput }
                >
                  {props.loading ? <Spinner /> : props.ConfirmButton}
                </Button>
              ) : (
                <Button
                  colorScheme={props.color ? props.color : "red"}
                  onClick={props.onClickFunction}
                  ml={3}
                  isDisabled={props.reason && !props.reasonInput }
                >
                  {props.ConfirmButton}
                </Button>
              )}
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </>
  );
};

export default ConfirmationModal;