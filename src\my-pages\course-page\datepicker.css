/* styles.css */

.react-datepicker__input-container input {
    width: 100%;
    padding: 8px 16px;
    font-size: 16px;
    border: 1.40px solid black;
    border-color: inherit;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    outline: 2px solid transparent;
  }
  
  .react-datepicker__input-container input:focus {
    z-index: 1;
    border-color: #3182ce;
    box-shadow: 0 0 0 1px #3182ce;
  }
  
  .react-datepicker__header {
    background-color: #f0f0f0;
    border-bottom: none;
  }
  
  .react-datepicker__month {
    margin: 0;
  }
  
  .react-datepicker__day-name,
  .react-datepicker__day,
  .react-datepicker__time-name {
    width: 30px;
    line-height: 30px;
    margin: 0;
    border-radius: 50%;
  }
  
  .react-datepicker__day--selected {
    background-color: #007bff;
    color: #fff;
  }
  
  .react-datepicker__day--keyboard-selected {
    background-color: #ccc;
  }
  
  /* Add specific style for dropdown */
  .react-datepicker__time-container {
    z-index: 99; /* Adjust the value as needed */
  }
  
  /* Add error style */
  .dateError {
    z-index: 1;
    border-color: #e53e3e;
    box-shadow: 0 0 0 1px #e53e3e;
  }