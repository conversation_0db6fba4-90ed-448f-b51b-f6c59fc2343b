/* pagination.css */
.pagination {
  display: flex;
  justify-content: center;
  list-style: none;
}

.pagination li {
  margin: 0 5px;
  cursor: pointer;
  padding: 4px 8px; /* Slightly larger padding */
  border: 1px solid #ccc;
  border-radius: 5px; /* Slightly rounded corners */
  transition: background-color 0.3s ease;
  font-size: 1rem; /* Adjusted font size */
}

.pagination li:hover {
  background-color: #f0f0f0;
}

.pagination li.active {
  font-weight: bold;
  background-color: #007bff;
  color: #fff;
  border: 1px solid #007bff;
}

.pagination li.disabled {
  pointer-events: none;
  color: #aaa;
  border-color: #aaa;
}

.pagination li.disabled:hover {
  background-color: transparent;
}
