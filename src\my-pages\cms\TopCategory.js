import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import { Md<PERSON><PERSON><PERSON>, MdDragHandle } from "react-icons/md";
import {
  Box,
  BreadcrumbLink,
  Input,
  Breadcrumb,
  BreadcrumbItem,
  Card,
  CardBody,
  Button,
  Stack,
  Text,
  Flex,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  TableContainer,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  useToast,
  Spinner,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
  Tooltip,
} from "@chakra-ui/react";
import { Link } from "react-router-dom";
import axios from "axios";
import { useNavigate } from "react-router-dom";

import { useSelector } from "react-redux";

const TopCategory = () => {
  const [categoryData, setCategoryData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [cmsCategoryData, setCmsCategoryData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });

  const [isUpdated, setIsUpdated] = useState(false);
  const [draggedItemIndex, setDraggedItemIndex] = useState(null);
  const [saveChangesBtnLoading, setSaveChangesBtnLoading] = useState(false);
  const [adjustBtnEdit, setAdjustBtnEdit] = useState(false);
  const [prevCategoryData, setPrevCategoryData] = useState([]);
  const [posBtnLoading, setPosBtnLoading] = useState(false);
  const [isDeleted, setIsDeleted] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [isOpen1, setIsOpen1] = useState(false);
  const onClose1 = () => setIsOpen1(false);
  const onOpen1 = () => setIsOpen1(true);

  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getCategoryData = (query) => {
    setCategoryData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/category?${
        searchQuery.length > 0 ? "page=1&name=" + query : "page=1"
      }`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setCategoryData({
          result: response.data.data,
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        setCategoryData({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const getcmsCategoryData = () => {
    setCmsCategoryData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/category`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    axios
      .request(config)
      .then((response) => {
        setCmsCategoryData({
          result: response.data.sort((a, b) => a.position - b.position),
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        setCmsCategoryData({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const updateAndSave = () => {
    onClose1();
    setSaveChangesBtnLoading(true);
    const getIds = cmsCategoryData.result.map((id, index) => {
      return { category: id.category._id, position: index + 1 };
    });

    let data = JSON.stringify({
      documents: getIds,
    });
    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/category/add`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        console.log(JSON.stringify(response.data));
        getcmsCategoryData();
        getCategoryData(searchQuery);
        setSaveChangesBtnLoading(false);
        setIsUpdated(false);
        onClose();
        setIsDeleted(false);
        toast({
          title: "category updated successfully",
          status: "success",
          duration: 3500,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setSaveChangesBtnLoading(false);
        if (
          error.response.data.message ===
          "Top Category cannot be greater than 15"
        ) {
          getcmsCategoryData();
          setIsUpdated(false);
          toast({
            title: error.response.data.message,
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handleDragStart = (index) => {
    setDraggedItemIndex(index);
  };

  const handleDragOver = (index) => {
    if (draggedItemIndex === null) return;
    const newItems = [...cmsCategoryData.result]; // Copy the result array from blockData
    const draggedItem = newItems[draggedItemIndex];
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);

    // Adjust positions of items inside newItems array
    newItems.forEach((item, idx) => {
      item.position = idx + 1;
    });

    setCmsCategoryData({ ...cmsCategoryData, result: newItems }); // Update result array in blockData
    setDraggedItemIndex(index);
  };

  const handleDragEnd = () => {
    setDraggedItemIndex(null);
  };

  const updateBlockPosition = () => {
    setPosBtnLoading(true);
    const value = cmsCategoryData.result.map((x) => {
      return { id: x._id, newPosition: x.position };
    });
    let data = JSON.stringify({
      updates: value,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/category/position`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setPosBtnLoading(false);
        setPrevCategoryData([]);
        setAdjustBtnEdit(false);
        toast({
          title: "Block position updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        onClose1();
        setPosBtnLoading(false);
        setPrevCategoryData([]);
        setAdjustBtnEdit(false);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  useEffect(() => {
    getcmsCategoryData();
  }, []);

  return (
    <Layout title="CMS | Top category" content="container">
      <Flex justifyContent={"space-between"} alignItems={"center"}>
        <Breadcrumb fontWeight="medium" fontSize="sm">
          <BreadcrumbItem>
            <BreadcrumbLink>
              <Link to={"/"}>Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>

          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink>CMS</BreadcrumbLink>
          </BreadcrumbItem>

          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink href="#">Top Categories</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>
        <Flex>
          {!adjustBtnEdit ? (
            userData?.accessScopes?.cms?.includes("write") && (
              <>
                {isDeleted ? (
                  !(!cmsCategoryData?.isLoading && cmsCategoryData?.error) &&
                  !adjustBtnEdit && (
                    <Flex justifyContent={"flex-end"} alignItems={"center"}>
                      <Button
                        variant={"outline"}
                        colorScheme="red"
                        size={"sm"}
                        py={5}
                        px={4}
                        mr={4}
                        isDisabled={!isUpdated}
                        onClick={() => {
                          setIsDeleted(false);
                          getcmsCategoryData();
                          setIsUpdated(false);
                        }}
                      >
                        Discard
                      </Button>
                      <Button
                        variant={"outline"}
                        colorScheme="green"
                        size={"sm"}
                        py={5}
                        px={4}
                        isDisabled={!isUpdated}
                        onClick={onOpen1}
                        isLoading={saveChangesBtnLoading}
                      >
                        Save Changes
                      </Button>
                    </Flex>
                  )
                ) : (
                  <Box>
                    <Button
                      variant={"outline"}
                      colorScheme="telegram"
                      size={"sm"}
                      py={5}
                      px={4}
                      mr={3}
                      onClick={() => {
                        setAdjustBtnEdit(true);
                        setPrevCategoryData(cmsCategoryData.result);
                      }}
                    >
                      Adjust Position
                    </Button>
                    <Button
                      variant={"outline"}
                      colorScheme="teal"
                      size={"sm"}
                      py={5}
                      px={4}
                      onClick={() => {
                        onOpen();
                        getCategoryData("");
                      }}
                      mr={2}
                      isDisabled={
                        !cmsCategoryData?.isLoading && cmsCategoryData?.error
                      }
                    >
                      Add Category
                    </Button>
                  </Box>
                )}
              </>
            )
          ) : (
            <Flex>
              <Button
                variant={"outline"}
                colorScheme="red"
                size={"sm"}
                py={5}
                px={4}
                mr={4}
                onClick={() => {
                  setCmsCategoryData({
                    result: prevCategoryData,
                    isLoading: false,
                    error: false,
                  });
                  setAdjustBtnEdit(false);
                }}
              >
                Discard
              </Button>
              <Button
                variant={"outline"}
                colorScheme="green"
                size={"sm"}
                py={5}
                px={4}
                isLoading={posBtnLoading}
                onClick={updateBlockPosition}
              >
                Save Changes
              </Button>
            </Flex>
          )}
        </Flex>
      </Flex>
      {/* Added/Selected Course List */}
      {!cmsCategoryData?.isLoading && cmsCategoryData?.error ? (
        <Flex
          justifyContent={"center"}
          alignItems={"center"}
          w={"full"}
          my={10}
        >
          <Text color={"red.500"}>
            Something went wrong please try again later...
          </Text>
        </Flex>
      ) : (
        <TableContainer
          mt={6}
          height={`${window.innerHeight - 200}px`}
          overflowY={"scroll"}
        >
          <Table variant="simple">
            <Thead
              bgColor={"#c1eaee"}
              position={"sticky"}
              top={"0px"}
              zIndex={"99"}
            >
              <Tr bgColor={"#E2DFDF"}>
                <Th>Position</Th>
                <Th>Name</Th>
                <Th>Description</Th>
                <Th textAlign={"center"}>Action</Th>
              </Tr>
            </Thead>
            <Tbody>
              {cmsCategoryData?.isLoading && !cmsCategoryData?.error ? (
                <Tr>
                  <Td></Td>
                  <Td> </Td>
                  <Td
                    display={"flex"}
                    justifyContent={"center"}
                    alignItems={"center"}
                  >
                    <Spinner />
                  </Td>
                  <Td></Td>
                </Tr>
              ) : (
                cmsCategoryData?.result?.map((category, inx) => {
                  return (
                    <>
                      {adjustBtnEdit ? (
                        <Tr
                          key={category._id}
                          draggable
                          onDragStart={() => handleDragStart(inx)}
                          onDragOver={() => handleDragOver(inx)}
                          onDragEnd={handleDragEnd}
                        >
                          <Td fontSize={"14px"}>{inx + 1 + "."}</Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {category?.category?.name || "n/a"}
                          </Td>
                          <Td fontSize={"14px"}>
                            {category?.category?.description
                              ? category?.category?.description?.substring(
                                  0,
                                  40
                                ) + "..."
                              : "n/a"}
                          </Td>

                          <Td fontSize={"14px"}>
                            {!adjustBtnEdit ? (
                              userData?.accessScopes?.cms?.includes(
                                "delete"
                              ) && (
                                <Flex
                                  justifyContent={"center"}
                                  alignItems={"center"}
                                  cursor={"pointer"}
                                  onClick={() => {
                                    setIsUpdated(true);
                                    setCmsCategoryData((prevState) => ({
                                      ...prevState,
                                      result: prevState?.result.filter(
                                        (obj) =>
                                          obj?.category?._id !==
                                          category?.category?._id
                                      ),
                                    }));
                                  }}
                                >
                                  <Tooltip label="Delete category">
                                    <Text>
                                      <MdDelete fontSize={"24px"} />
                                    </Text>
                                  </Tooltip>
                                </Flex>
                              )
                            ) : (
                              <Flex
                                justifyContent={"center"}
                                alignItems={"center"}
                              >
                                <Text as={"span"} ml={3}>
                                  <MdDragHandle
                                    style={{ cursor: "grab" }}
                                    fontSize={"24px"}
                                  />
                                </Text>
                              </Flex>
                            )}
                          </Td>
                        </Tr>
                      ) : (
                        <Tr key={category._id}>
                          <Td fontSize={"14px"}>{inx + 1 + "."}</Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {category?.category?.name || "n/a"}
                          </Td>
                          <Td fontSize={"14px"}>
                            {category?.category?.description
                              ? category?.category?.description?.substring(
                                  0,
                                  40
                                ) + "..."
                              : "n/a"}
                          </Td>

                          <Td fontSize={"14px"}>
                            {!adjustBtnEdit ? (
                              userData?.accessScopes?.cms?.includes(
                                "delete"
                              ) && (
                                <Flex
                                  justifyContent={"center"}
                                  alignItems={"center"}
                                  cursor={"pointer"}
                                  onClick={() => {
                                    setIsUpdated(true);
                                    setIsDeleted(true);
                                    setCmsCategoryData((prevState) => ({
                                      ...prevState,
                                      result: prevState?.result.filter(
                                        (obj) =>
                                          obj?.category?._id !==
                                          category?.category?._id
                                      ),
                                    }));
                                  }}
                                >
                                  <Tooltip label="Delete category">
                                    <Text>
                                      <MdDelete fontSize={"24px"} />
                                    </Text>
                                  </Tooltip>
                                </Flex>
                              )
                            ) : (
                              <Flex
                                justifyContent={"center"}
                                alignItems={"center"}
                              >
                                <Text as={"span"} ml={3}>
                                  <MdDragHandle
                                    style={{ cursor: "grab" }}
                                    fontSize={"24px"}
                                  />
                                </Text>
                              </Flex>
                            )}
                          </Td>
                        </Tr>
                      )}
                    </>
                  );
                })
              )}
            </Tbody>
          </Table>
        </TableContainer>
      )}
    
      {/* Modal for course search */}
      <Modal isOpen={isOpen} onClose={onClose} size="5xl">
        <ModalOverlay />
        <ModalContent>
        <ModalHeader>
            <Flex justifyContent={"space-between"} alignItems={"center"}>
              <Text mb={0}>Search Categories</Text>
              <Flex>
                {!(!cmsCategoryData?.isLoading && cmsCategoryData?.error) &&
                  !adjustBtnEdit &&
                  userData?.accessScopes?.cms?.includes("write") && (
                    <Flex
                      justifyContent={"flex-end"}
                      alignItems={"center"}
                      mr={7}
                    >
                      <Button
                        variant={"outline"}
                        colorScheme="red"
                        size={"sm"}
                        py={3}
                        px={4}
                        mr={2}
                        isDisabled={!isUpdated}
                        onClick={() => {
                          getcmsCategoryData();
                          setIsUpdated(false);
                          onClose();
                        }}
                      >
                        Discard
                      </Button>
                      <Button
                        variant={"outline"}
                        colorScheme="green"
                        size={"sm"}
                        py={3}
                        px={4}
                        isDisabled={!isUpdated}
                        onClick={onOpen1}
                        isLoading={saveChangesBtnLoading}
                      >
                        Save Changes
                      </Button>
                    </Flex>
                  )}
                <ModalCloseButton />
              </Flex>
            </Flex>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Card mt={3} variant="outline" height={"95%"}>
              <CardBody>
                <Box>
                  <Stack direction="row" spacing={4} align="center">
                    <Input
                      type="text"
                      placeholder="Search category"
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={(e) =>
                        e.key === "Enter" && getCategoryData(searchQuery)
                      }
                    />
                    <Button
                      variant="solid"
                      colorScheme="telegram"
                      onClick={() => getCategoryData(searchQuery)}
                      isDisabled={!(searchQuery.length >= 3)}
                    >
                      Search
                    </Button>
                  </Stack>
                  {/* Searched Category List */}
                  {!categoryData?.isLoading && categoryData?.error ? (
                    <Flex
                      justifyContent={"center"}
                      alignItems={"center"}
                      w={"full"}
                      my={10}
                    >
                      <Text color={"red.500"}>
                        Something went wrong please try again later...
                      </Text>
                    </Flex>
                  ) : categoryData.result.length > 0 ? (
                    <TableContainer
                      mt={6}
                      height={`${window.innerHeight - 300}px`}
                      overflowY={"scroll"}
                    >
                      <Table variant="simple">
                        <Thead
                          bgColor={"#c1eaee"}
                          position={"sticky"}
                          top={"0px"}
                          zIndex={"99"}
                        >
                          <Tr bgColor={"#E2DFDF"}>
                            <Th>S.No</Th>
                            <Th>Name</Th>
                            <Th>Description</Th>
                            <Th textAlign={"center"}>Action</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {categoryData?.isLoading && !categoryData?.error ? (
                            <Tr>
                              <Td></Td>
                              <Td> </Td>
                              <Td
                                display={"flex"}
                                justifyContent={"center"}
                                alignItems={"center"}
                              >
                                <Spinner />
                              </Td>
                              <Td></Td>
                            </Tr>
                          ) : (
                            categoryData?.result?.map((category, inx) => {
                              return (
                                <Tr key={category._id}>
                                  <Td textAlign={"start"}>{inx + 1}</Td>
                                  <Td
                                    style={{
                                      whiteSpace: "pre-wrap",
                                      wordWrap: "break-word",
                                    }}
                                    textAlign={"start"}
                                  >
                                    {category?.name || "n/a"}
                                  </Td>
                                  <Td textAlign={"start"}>
                                    {category?.description
                                      ? category?.description?.substring(
                                          0,
                                          40
                                        ) + "..."
                                      : "n/a"}
                                  </Td>
                                  <Td>
                                    {cmsCategoryData?.result.every(
                                      (z) => z?.category?._id !== category?._id
                                    ) ? (
                                      <Button
                                        colorScheme="telegram"
                                        size={"sm"}
                                        onClick={() => {
                                          setIsUpdated(true);
                                          setCmsCategoryData((prevState) => ({
                                            ...prevState,
                                            result: [
                                              ...prevState?.result,
                                              { category: category },
                                            ],
                                          }));
                                        }}
                                      >
                                        Add
                                      </Button>
                                    ) : (
                                      <Button
                                        colorScheme="red"
                                        size={"sm"}
                                        onClick={() => {
                                          setIsUpdated(true);
                                          setCmsCategoryData((prevState) => ({
                                            ...prevState,
                                            result: prevState?.result.filter(
                                              (obj) =>
                                                obj?.category?._id !==
                                                category?._id
                                            ),
                                          }));
                                        }}
                                      >
                                        Remove
                                      </Button>
                                    )}
                                  </Td>
                                </Tr>
                              );
                            })
                          )}
                        </Tbody>
                      </Table>
                    </TableContainer>
                  ) : categoryData?.isLoading && !categoryData?.error ? (
                    <Flex
                      justifyContent={"center"}
                      alignItems={"center"}
                      mt={6}
                    >
                      <Spinner size={"lg"} />
                    </Flex>
                  ) : (
                    <Flex
                      justifyContent={"center"}
                      alignItems={"center"}
                      mt={6}
                    >
                      <Text
                        fontSize={"18px"}
                        fontWeight={"semibold"}
                        color={"red.300"}
                      >
                        Result not found
                      </Text>
                    </Flex>
                  )}
                </Box>
              </CardBody>
            </Card>
          </ModalBody>
        </ModalContent>
      </Modal>
      {/* Save Changes Alert */}
      <AlertDialog
        motionPreset="slideInBottom"
        onClose={onClose1}
        isOpen={isOpen1}
        isCentered
      >
        <AlertDialogOverlay />

        <AlertDialogContent>
          <AlertDialogHeader>Save Changes</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            Are you sure you want to make these changes.
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button onClick={onClose1}>No</Button>
            <Button
              colorScheme="telegram"
              ml={3}
              onClick={() => updateAndSave()}
            >
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
};

export default TopCategory;
