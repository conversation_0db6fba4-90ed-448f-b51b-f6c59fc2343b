import React, { useState, useEffect } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import Layout from "../../layout/default";
import {
  FormControl,
  FormLabel,
  Input,
  Select,
  FormErrorMessage,
  Button,
  VStack,
  useToast,
  Card,
  CardBody,
  ModalContent,
  ModalFooter,
  Modal,
  ModalOverlay,
  ModalCloseButton,
  ModalBody,
  Container,
  Avatar,
  useDisclosure,
  Box,
  Flex,
  Heading,
  // MenuItem
} from "@chakra-ui/react";
import { useNavigate, useParams } from "react-router-dom";
import axios from "axios";

const PlayerForm = () => {
  const { id } = useParams();

  const { isOpen, onClose, onOpen } = useDisclosure();

  const toast = useToast();
  const navigate = useNavigate()

  const [preview, setPreview] = useState("");

  const phoneRegExp = /^[6-9]\d{9}$/;
  const pinCodeRegExp = /^[1-9][0-9]{5}$/

  const validationSchema = Yup.object().shape({
    firstName: Yup.string().required("First Name is required"),
    lastName: Yup.string().required("Last Name is required"),
    email: Yup.string().email("Invalid email").required("Email is required"),
    dob: Yup.date()
      .required("Date of birth is required")
      .max(new Date(), "Date of birth cannot be in the future")
      .test("is-adult", "Must be at least 18 years old", function (value) {
        const today = new Date();
        const minAgeDate = new Date(
          today.getFullYear() - 18,
          today.getMonth(),
          today.getDate()
        );
        return value <= minAgeDate;
      }),
    password: Yup.string()
      .min(6, "Password must be at least 6 characters")
      .required("Password is required"),
    gender: Yup.string().required("Gender is required"),
    mobile: Yup.string()
      .matches(phoneRegExp, "Phone number is not valid")
      .required("Please enter your phone number"),
    profileImage: Yup.string(),
    address: Yup.array().of(
      Yup.object().shape({
        addressLine1: Yup.string().required("Address Line 1 is required"),
        addressLine2: Yup.string(),
        city: Yup.string().required("City is required"),
        state: Yup.string().required("State is required"),
        pinCode: Yup.string()
          .matches(pinCodeRegExp, "PIN code is not valid")
          .required("pinCode is required"),
        country: Yup.string().required("Country is required"),
      })
    ),
  });

  const getPlayerDetails = async() => {
    try {
      if(id){
        let data = await axios.get(`http://localhost:5000/api/player/${id}`);
        formik.setValues({...formik.values, ...data.data})
      }
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    getPlayerDetails()
  }, [id, toast])
  


  const initialValues = {
    firstName: "",
    lastName: "",
    mobile: "",
    email: "",
    password: "",
    dob: "",
    gender: "",
    profileImage: "",
    address: [
      {
        addressLine1: "",
        addressLine2: "",
        city: "",
        state: "",
        pinCode: "",
        country: "",
      },
    ],
  };

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values) => {


      try {

        // const age = calculateAge(values.dob);
        // formik.setFieldValue('age', age)
  
        // let object = {...values, categories:category}
        // delete object.email

        // console.log(object)


        const options = {
          method: 'POST',
          url: `http://localhost:5000/api/player/create`,
          headers: {
            'Content-Type': 'application/json'
          },
          data: values
        };
    
        const response = await axios.request(options);

    if(response?.status == 200){
      
      toast({
        title: "User Updated.",
        description: "You successfully updated the details.",
        status: "success",
        duration: 3000,
        isClosable: true,
      });

      navigate(`/coach-list`)
    }else{
      toast({
        title: "Error Occurred!",
        description: "Please try again!",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
    
      } catch (error) {
        toast({
          title: "Error Occurred!",
          description: "Please try again!",
          status: "error",
          duration: 3000,
          isClosable: true,
        });
    
        console.error(error.message, "oo");
      }
    },
  });

  const imageSubmitHandler = async (e, image) => {
    e.preventDefault();

    const formData = new FormData();
    formData.append("image", image);
    if (formik.values.profileImg) {
      formData.append("url", formik.values.profileImg);
    }

    try {
      const response = await axios.post(
        "http://localhost:5000/api/coach/uploadImage",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response.data.url;
      const uploadData = { ...formik.values, profileImg: url };
      delete uploadData.email;

      await axios.patch(`http://localhost:5000/api/coach/${id}`, uploadData, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      const showUrl = await axios.post(
        "http://localhost:5000/api/coach/download",
        { location: url },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      setPreview(showUrl.data.url);
      onClose();
      toast({
        title: "Picture Updated.",
        description: "You successfully updated your profile photo",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.log(error);
    }
  };


    const addAddress = () => {
    formik.setValues({
      ...formik.values,
      address: [
        ...formik.values.address,
        {
          addressLine1: "",
          addressLine2: "",
          city: "",
          state: "",
          pinCode: "",
          country: "",
        },
      ],
    });
  };


  return (
    <Layout>
      <Card>
        <CardBody>
          <VStack spacing={4}>
            {id ? <h2>Player Details</h2> : <h2>Edit Player Details</h2>}
            {/* {id ?  <Button leftIcon={<MdModeEditOutline />} colorScheme="yellow" left={['0px', '200px', '260px', '270px', '450px', '550px']} onClick={()=>setIsEditable(!isEditable)}>Edit</Button>: null} */}
            <form className="col-md-12" onSubmit={formik.handleSubmit}>
              {/* <fieldset disabled={!isEditable}> */}
              <VStack>
                <Avatar
                  boxSize={"40"}
                  src={preview}
                  name={formik.values.firstName}
                />
                <Button colorScheme="yellow" variant="ghost" onClick={onOpen}>
                  {id ? "Update Profile Image" : "Add Profile Image"}
                </Button>
              </VStack>
              <br />

              <div className="col-md-12 row">
                <div className="col-md-6">
                  <FormControl
                    isInvalid={
                      formik.touched.firstName && formik.errors.firstName
                    }
                    isRequired
                  >
                    <FormLabel htmlFor="firstName">First Name</FormLabel>
                    <Input
                      type="text"
                      id="firstName"
                      name="firstName"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.firstName}
                    />
                    <FormErrorMessage>
                      {formik.errors.firstName}
                    </FormErrorMessage>
                  </FormControl>
                </div>

                <div className="col-md-6">
                  <FormControl
                    isInvalid={
                      formik.touched.lastName && formik.errors.lastName
                    }
                    isRequired
                  >
                    <FormLabel htmlFor="lastName">Last Name</FormLabel>
                    <Input
                      type="text"
                      id="lastName"
                      name="lastName"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.lastName}
                    />
                    <FormErrorMessage>
                      {formik.errors.lastName}
                    </FormErrorMessage>
                  </FormControl>
                </div>
              </div>
              <br />

              <div className="col-md-12 row">
                <div className="col-md-6">
                  <FormControl
                    isInvalid={formik.touched.gender && formik.errors.gender}
                    isRequired
                  >
                    <FormLabel htmlFor="gender">Gender</FormLabel>
                    <Select
                      id="gender"
                      name="gender"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.gender}
                    >
                      <option value="">Select Gender</option>
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                      <option value="other">Other</option>
                    </Select>
                    <FormErrorMessage>{formik.errors.gender}</FormErrorMessage>
                  </FormControl>
                </div>

                <div className="col-md-6">
                  <FormControl
                    isInvalid={formik.touched.email && formik.errors.email}
                    isRequired
                  >
                    <FormLabel htmlFor="email">Email</FormLabel>
                    <Input
                      type="email"
                      id="email"
                      name="email"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.email}
                    />
                    <FormErrorMessage>{formik.errors.email}</FormErrorMessage>
                  </FormControl>
                </div>
              </div>
              <br />

              <div className="col-md-12 row">
                <div className="col-md-6">
                  <FormControl
                    isInvalid={
                      formik.touched.password && formik.errors.password
                    }
                  >
                    <FormLabel htmlFor="password">Password</FormLabel>
                    <Input
                      type="password"
                      id="password"
                      name="password"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.password}
                    />
                    <FormErrorMessage>
                      {formik.errors.password}
                    </FormErrorMessage>
                  </FormControl>
                </div>
                <div className="col-md-6">
                  <FormControl
                    isInvalid={formik.touched.mobile && formik.errors.mobile}
                    isRequired
                  >
                    <FormLabel htmlFor="mobile">Mobile</FormLabel>
                    <Input
                      type="text"
                      id="mobile"
                      name="mobile"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.mobile}
                    />
                    <FormErrorMessage>{formik.errors.mobile}</FormErrorMessage>
                  </FormControl>
                </div>
              </div>
              <br /> 

              <div className="col-md-12 row">
                <div className="col-md-12">
                  <FormControl
                    isInvalid={formik.touched.dob && formik.errors.dob}
                    isRequired
                  >
                    <FormLabel htmlFor="dob">Date of Birth</FormLabel>
                    <Input
                      type="date"
                      id="dob"
                      name="dob"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.dob}
                    />
                    <FormErrorMessage>{formik.errors.dob}</FormErrorMessage>
                  </FormControl>
                </div>
              </div>
              <br />

              <div className="col-md-12 row">
              <div className="col-md-9"><Heading alignItems={"center"}>Addresses</Heading></div>
              <div className="col-md-3 ">
                <Button float="right" colorScheme="blue" onClick={addAddress}>
                  Add Address
                </Button>
              </div>
            </div><br/>

              {formik.values.address.map((address, index) => (
              <Box
                key={index}
                mb={4}
                p={4}
                w="100%"
                borderWidth="1px"
                borderRadius="lg"
              >
                <Flex justifyContent="space-between" alignItems="center" mb={4}>
                  <h3>Address {index + 1}</h3>
                  <Button
                    type="button"
                    colorScheme="red"
                    size="sm"
                    onClick={() =>
                      formik.setValues((prevState) => ({
                        ...prevState,
                        address: prevState.address.filter(
                          (_, i) => i !== index
                        ),
                      }))
                    }
                  >
                    Remove
                  </Button>
                </Flex>
                <div className="col-md-12 row">
                  <div className="col-md-6">
                  <FormControl
                      isInvalid={
                        formik.touched.address?.[index]?.addressLine1 &&
                        formik.errors.address?.[index]?.addressLine1
                      }
                      isRequired
                    >
                      <FormLabel>Address Line 1</FormLabel>
                      <Input
                        name={`address.${index}.addressLine1`}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values.address[index].addressLine1}
                      />
                       <FormErrorMessage>
                        {formik.touched.address?.[index]?.addressLine1 &&
                          formik.errors.address?.[index]?.addressLine1}
                      </FormErrorMessage>
                    </FormControl>
                  </div>

                  <div className="col-md-6">
                  <FormControl
                      isInvalid={
                        formik.touched.address?.[index]?.addressLine2 &&
                        formik.errors.address?.[index]?.addressLine2
                      }
                    >
                      <FormLabel>Address Line 2</FormLabel>
                      <Input
                        name={`address.${index}.addressLine2`}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values.address[index].addressLine2}
                      />
                       <FormErrorMessage>
                        {formik.touched.address?.[index]?.addressLine2 &&
                          formik.errors.address?.[index]?.addressLine2}
                      </FormErrorMessage>
                    </FormControl>
                  </div>
                </div>

                <div className="col-md-12 row">
                  <div className="col-md-6">
                  <FormControl
                      isInvalid={
                        formik.touched.address?.[index]?.city &&
                        formik.errors.address?.[index]?.city
                      }
                      isRequired
                    >
                      <FormLabel>City</FormLabel>
                      <Input
                        name={`address.${index}.city`}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values.address[index].city}
                      />
                       <FormErrorMessage>
                        {formik.touched.address?.[index]?.city &&
                          formik.errors.address?.[index]?.city}
                      </FormErrorMessage>
                    </FormControl>
                  </div>

                  <div className="col-md-6">
                  <FormControl
                      isInvalid={
                        formik.touched.address?.[index]?.state &&
                        formik.errors.address?.[index]?.state
                      }
                      isRequired
                    >
                      <FormLabel>State</FormLabel>
                      <Input
                        name={`address.${index}.state`}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values.address[index].state}
                      />
                       <FormErrorMessage>
                        {formik.touched.address?.[index]?.state &&
                          formik.errors.address?.[index]?.state}
                      </FormErrorMessage>
                    </FormControl>
                  </div>
                </div>

                <div className="col-md-12 row">
                  <div className="col-md-6">
                  <FormControl
                      isInvalid={
                        formik.touched.address?.[index]?.pinCode &&
                        formik.errors.address?.[index]?.pinCode
                      }
                      isRequired
                    >
                      <FormLabel>Pincode</FormLabel>
                      <Input
                        name={`address.${index}.pinCode`}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        value={formik.values.address[index].pinCode}
                      />
                       <FormErrorMessage>
                        {formik.touched.address?.[index]?.pinCode &&
                          formik.errors.address?.[index]?.pinCode}
                      </FormErrorMessage>
                    </FormControl>
                  </div>

                  <div className="col-md-6">
                  <FormControl
                      isInvalid={
                        formik.touched.address?.[index]?.country &&
                        formik.errors.address?.[index]?.country
                      }
                      isRequired
                    >
                      <FormLabel>Country</FormLabel>
                      <Input
                        name={`address.${index}.country`}
                        onBlur={formik.handleBlur}
                        onChange={formik.handleChange}
                        value={formik.values.address[index].country}
                      />
                       <FormErrorMessage>
                        {formik.touched.address?.[index]?.country &&
                          formik.errors.address?.[index]?.country}
                      </FormErrorMessage>
                    </FormControl>
                  </div>
                </div>              
              </Box>
            ))}

              <Button
                colorScheme="blue"
                w="100%"
                type="submit"
                onClick={formik.handleSubmit}
              >
                Save Changes
              </Button>
              {/* </fieldset> */}
            </form>
          </VStack>
        </CardBody>
      </Card>

      <ChangePhotoModal
        isOpen={isOpen}
        onClose={onClose}
        imageSubmitHandler={imageSubmitHandler}
      />
    </Layout>
  );
};

export default PlayerForm;

function ChangePhotoModal({ isOpen, onClose, imageSubmitHandler }) {
  const [image, setImage] = useState("");
  const [imagePrev, setImagePrev] = useState("");

  const changeImage = (e) => {
    const file = e.target.files[0];
    const reader = new FileReader();

    reader.readAsDataURL(file);
    reader.onloadend = () => {
      setImagePrev(reader.result);
      setImage(file);
    };
  };

  const closehandler = () => {
    onClose();
    setImage("");
    setImagePrev("");
  };

  return (
    <Modal isOpen={isOpen} onClose={closehandler}>
      <ModalOverlay backdropFilter={"blur(10px)"} />

      <ModalContent>
        <ModalCloseButton />

        <ModalBody>
          <Container>
            <form onSubmit={(e) => imageSubmitHandler(e, image)}>
              <VStack spacing={"8"}>
                {imagePrev && <Avatar src={imagePrev} boxSize={"48"} />}

                <Input type="file" onChange={changeImage} />

                <Button w="full" colorScheme="yellow" type="submit">
                  Save Profile Photo
                </Button>
              </VStack>
            </form>
          </Container>
        </ModalBody>

        <ModalFooter>
          <Button mr="3" onClick={closehandler}>
            Cancel
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
