import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";
import {
  Button,
  Checkbox,
  Divider,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  ModalBody,
  ModalFooter,
  Select,
  Stack,
  Text,
  useToast,
} from "@chakra-ui/react";
import { getCookie } from "../../utilities/auth";

const EditBlockModal = ({ viewBlock, onClose, blockId, renderMeFn }) => {
  const [isBtnLoading, setIsBtnLoading] = useState(false);
  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const validationSchema = Yup.object().shape({
    title: Yup.string()
      .required("Title is required")
      .min(3, "Title should be minimum 3 characters"),
    visibility: Yup.string().required("Visibility is required"),
    // min: Yup.number().nullable().positive("Please enter a positive value"),
    max: Yup.number().nullable().positive("Please enter a positive value"),
  });
  const formik = useFormik({
    initialValues: viewBlock,
    validationSchema: validationSchema,
    onSubmit: (values) => {
      setIsBtnLoading(true);
      let data = JSON.stringify(values);
      let config = {
        method: "patch",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/cms/update/block/${blockId}`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {
          onClose();
          setIsBtnLoading(false);
          renderMeFn();
          toast({
            title: "Block updated successfully",
            status: "success",
            position: "top",
            duration: 3500,
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log(error);
          if (
            error.response.data.message ===
            "Max value cannot be greater than 15"
          ) {
            onClose();
            setIsBtnLoading(false);
            toast({
              title: `${error.response.data.message}`,
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else if (error.response.status === 403) {
            toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    },
  });

  return (
    <form onSubmit={formik.handleSubmit}>
      <ModalBody>
        <FormControl
          mb={3}
          isInvalid={formik.touched.title && formik.errors.title}
        >
          <FormLabel htmlFor="title">Title</FormLabel>
          <Input
            id="title"
            name="title"
            type="text"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.title}
            placeholder="Enter title"
          />
          <FormErrorMessage>{formik.errors.title}</FormErrorMessage>
        </FormControl>
   
        <Flex justifyContent={"space-between"} alignItems={"center"} mt={3}>
          {/* <FormControl
            flexBasis={"48%"}
            isInvalid={formik.touched.min && formik.errors.min}
          >
            <FormLabel htmlFor="min">Minimum</FormLabel>
            <Input
              type="number"
              placeholder="Enter min no. of items to show"
              id="min"
              name="min"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.min || ""}
            />
            <FormErrorMessage>{formik.errors.min}</FormErrorMessage>
          </FormControl> */}
          {viewBlock.identity.toLowerCase() !== "registration" && (
            <FormControl
              flexBasis={"100%"}
              isInvalid={formik.touched.max && formik.errors.max}
            >
              <FormLabel htmlFor="max">Maximum</FormLabel>
              <Input
                type="number"
                placeholder="Enter max no. of items to show"
                id="max"
                name="max"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.max || ""}
              />
              <FormErrorMessage>{formik.errors.max}</FormErrorMessage>
            </FormControl>
          )}
        </Flex>

        <Flex justifyContent={'flex-start'} alignItems={'center'} mt={3}>
            <Text mr={3} mt={3} fontWeight={'semibold'}>Visibility</Text>
            <Checkbox
              colorScheme="blue"
              id="visibility"
              name="visibility"
              onChange={formik.handleChange}
              isChecked={formik.values.visibility}
            />
          </Flex>

      </ModalBody>
      <Divider />
      <ModalFooter>
        <Flex w={"full"} justifyContent={"space-between"} alignItems={"center"}>
          <Button
            colorScheme="red"
            onClick={() => {
              onClose();
              setIsBtnLoading(false);
            }}
            flexBasis={"48%"}
          >
            Discard
          </Button>
          <Button
            colorScheme="green"
            flexBasis={"48%"}
            type="submit"
            isLoading={isBtnLoading}
          >
            Save Changes
          </Button>
        </Flex>
      </ModalFooter>
    </form>
  );
};

export default EditBlockModal;
