import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import { MdEdit, MdDragHandle } from "react-icons/md";
import axios from "axios";

import {
  BreadcrumbLink,
  Breadcrumb,
  BreadcrumbItem,
  Text,
  Flex,
  Tooltip,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  TableContainer,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  Button,
  Spinner,
  useToast,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
} from "@chakra-ui/react";
import { Link } from "react-router-dom";
import EditBlockModal from "./EditBlockModal";
import { getCookie } from "../../utilities/auth";
import { useSelector } from "react-redux";

const BlocksCms = () => {
  const [blockData, setBlockData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [adjustBtnEdit, setAdjustBtnEdit] = useState(false);
  const [prevBlockData, setPrevBlockData] = useState([]);
  const [posBtnLoading, setPosBtnLoading] = useState(false);

  const [draggedItemIndex, setDraggedItemIndex] = useState(null);
  const token = sessionStorage.getItem("admintoken").split(" ")[1];

  const [viewBlock, setViewBlock] = useState({
    identity: "",
    title: "",
    visibility: "",
    position: null,
    // min: null,
    max: null,
  });
  const [blockId, setBlockId] = useState("");
  const [renderMe, setRenderMe] = useState(0);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [isOpen1, setIsOpen1] = useState(false);
  const onClose1 = () => setIsOpen1(false);
  const onOpen1 = () => setIsOpen1(true);

  const toast = useToast();
  const userData = useSelector((state) => state.user);

  const getBlocks = () => {
    setBlockData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/admin/blocks`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setBlockData({
          result: response.data.sort((a, b) => a.position - b.position),
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        setBlockData({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const renderMeFn = () => {
    setRenderMe((prev) => prev + 1);
  };

  const handleDragStart = (index) => {
    setDraggedItemIndex(index);
  };

  const handleDragOver = (index) => {
    if (draggedItemIndex === null) return;
    const newItems = [...blockData.result]; // Copy the result array from blockData
    const draggedItem = newItems[draggedItemIndex];
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);

    // Adjust positions of items inside newItems array
    newItems.forEach((item, idx) => {
      item.position = idx + 1;
    });

    setBlockData({ ...blockData, result: newItems }); // Update result array in blockData
    setDraggedItemIndex(index);
  };

  const handleDragEnd = () => {
    setDraggedItemIndex(null);
  };

  const updateBlockPosition = () => {
    setPosBtnLoading(true);
    const value = blockData.result.map((x) => {
      return { id: x._id, newPosition: x.position };
    });
    let data = JSON.stringify({
      updates: value,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/update/block/position`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setPosBtnLoading(false);
        setPrevBlockData([]);
        setAdjustBtnEdit(false);
        onClose1();
        toast({
          title: "Block position updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        onClose1();
        setPosBtnLoading(false);
        setPrevBlockData([]);
        setAdjustBtnEdit(false);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };
  useEffect(() => {
    getBlocks();
  }, [renderMe]);

  return (
    <Layout title="CMS | Blocks" content="container">
      <Flex justifyContent={"space-between"} alignItems={"center"}>
        <Breadcrumb fontWeight="medium" fontSize="sm">
          <BreadcrumbItem>
            <BreadcrumbLink>
              <Link to={"/"}>Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>

          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink>CMS</BreadcrumbLink>
          </BreadcrumbItem>

          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink href="#">Blocks</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>
        {adjustBtnEdit ? (
          <Flex>
            <Button
              variant={"outline"}
              colorScheme="red"
              size={"sm"}
              py={5}
              px={4}
              mr={4}
              onClick={() => {
                setBlockData({
                  result: prevBlockData,
                  isLoading: false,
                  error: false,
                });
                setAdjustBtnEdit(false);
              }}
            >
              Discard
            </Button>
            <Button
              variant={"outline"}
              colorScheme="green"
              size={"sm"}
              py={5}
              px={4}
              isLoading={posBtnLoading}
              onClick={onOpen1}
            >
              Save Changes
            </Button>
          </Flex>
        ) : (
          userData?.accessScopes?.cms?.includes("write") && (
            <Button
              variant={"outline"}
              colorScheme="telegram"
              size={"sm"}
              py={5}
              px={4}
              onClick={() => {
                setPrevBlockData(blockData.result);
                setAdjustBtnEdit(true);
              }}
            >
              Adjust Position
            </Button>
          )
        )}
      </Flex>
      {/* block List */}
      {!blockData?.isLoading && blockData?.error ? (
        <Flex
          justifyContent={"center"}
          alignItems={"center"}
          w={"full"}
          my={10}
        >
          <Text color={"red.500"}>
            Something went wrong please try again later...
          </Text>
        </Flex>
      ) : (
        <TableContainer
          mt={6}
          height={`${window.innerHeight - 185}px`}
          overflowY={"scroll"}
        >
          <Table variant="simple">
            <Thead
              bgColor={"#c1eaee"}
              position={"sticky"}
              top={"0px"}
              zIndex={"99"}
            >
              <Tr bgColor={"#E2DFDF"}>
                <Th>Position</Th>
                <Th>Identity</Th>
                <Th>Title</Th>
                <Th>Visibility</Th>
                <Th>Max</Th>
                {/* <Th>Min</Th> */}
                <Th textAlign={"center"}>Action</Th>
              </Tr>
            </Thead>
            <Tbody>
              {blockData?.isLoading && !blockData?.error ? (
                <Tr>
                  <Td></Td>
                  <Td></Td>
                  <Td
                    display={"flex"}
                    justifyContent={"center"}
                    alignItems={"center"}
                  >
                    <Spinner />
                  </Td>
                  <Td></Td>
                  <Td></Td>
                  <Td></Td>
                </Tr>
              ) : (
                blockData?.result?.map((blockData, inx) => {
                  return (
                    <>
                      {adjustBtnEdit ? (
                        <Tr
                          key={blockData._id}
                          draggable
                          onDragStart={() => handleDragStart(inx)}
                          onDragOver={() => handleDragOver(inx)}
                          onDragEnd={handleDragEnd}
                        >
                          <Td fontSize={"14px"}>{inx + 1 + "."}</Td>
                          <Td fontSize={"14px"}>{blockData?.identity}</Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {blockData?.title}
                          </Td>
                          <Td fontSize={"14px"}>
                            {blockData?.visibility ? "Visible" : "Not Visible"}
                          </Td>
                          <Td fontSize={"14px"}>{(blockData?.identity?.toLowerCase() !== "registration") && blockData?.max || "--"}</Td>
                          {/* <Td>{blockData?.min || "--"}</Td> */}
                          <Td fontSize={"14px"}>
                            {userData?.accessScopes?.cms?.includes("write") && (
                              <Flex>
                                {!adjustBtnEdit ? (
                                  <Tooltip label="Edit Block">
                                    <Flex
                                      color={"black.500"}
                                      justifyContent={"center"}
                                      alignItems={"center"}
                                      cursor={"pointer"}
                                      onClick={() => {
                                        onOpen();
                                        setBlockId(blockData._id);
                                        setViewBlock({
                                          identity: blockData?.identity,
                                          title: blockData?.title || "",
                                          visibility: blockData?.visibility,
                                          // min: blockData?.min,
                                          max: blockData?.max,
                                          position: Number(blockData?.position),
                                        });
                                      }}
                                    >
                                      <MdEdit fontSize={"24px"} />
                                    </Flex>
                                  </Tooltip>
                                ) : (
                                  <Text as={"span"} ml={3}>
                                    <MdDragHandle
                                      style={{ cursor: "grab" }}
                                      fontSize={"24px"}
                                    />
                                  </Text>
                                )}
                              </Flex>
                            )}
                          </Td>
                        </Tr>
                      ) : (
                        <Tr key={blockData._id}>
                          <Td fontSize={"14px"}>{inx + 1 + "."}</Td>
                          <Td fontSize={"14px"}>{blockData?.identity}</Td>
                          <Td
                            style={{
                              whiteSpace: "pre-wrap",
                              wordWrap: "break-word",
                            }}
                            fontSize={"14px"}
                          >
                            {blockData?.title}
                          </Td>
                          <Td fontSize={"14px"}>
                            {blockData?.visibility ? "Visible" : "Not Visible"}
                          </Td>
                          <Td fontSize={"14px"}>{(blockData?.identity?.toLowerCase() !== "registration") && (blockData?.max || "--")}</Td>
                          {/* <Td>{blockData?.min || "--"}</Td> */}
                          <Td fontSize={"14px"}>
                            {userData?.accessScopes?.cms?.includes("write") && (
                              <Flex>
                                {!adjustBtnEdit ? (
                                  <Tooltip label="Edit Block">
                                    <Flex
                                      color={"black.500"}
                                      justifyContent={"center"}
                                      alignItems={"center"}
                                      cursor={"pointer"}
                                      onClick={() => {
                                        onOpen();
                                        setBlockId(blockData._id);
                                        setViewBlock({
                                          identity: blockData?.identity,
                                          title: blockData?.title || "",
                                          visibility: blockData?.visibility,
                                          // min: blockData?.min,
                                          max: blockData?.max,
                                          position: Number(blockData?.position),
                                        });
                                      }}
                                    >
                                      <MdEdit fontSize={"24px"} />
                                    </Flex>
                                  </Tooltip>
                                ) : (
                                  <Text as={"span"} ml={3}>
                                    <MdDragHandle
                                      style={{ cursor: "grab" }}
                                      fontSize={"24px"}
                                    />
                                  </Text>
                                )}
                              </Flex>
                            )}
                          </Td>
                        </Tr>
                      )}
                    </>
                  );
                })
              )}
            </Tbody>
          </Table>
        </TableContainer>
      )}

      {/* Modal for course search */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Edit Block</ModalHeader>
          <ModalCloseButton />
          <EditBlockModal
            viewBlock={viewBlock}
            onClose={onClose}
            blockId={blockId}
            renderMeFn={renderMeFn}
          />
        </ModalContent>
      </Modal>

      {/* Save Changes Alert */}
      <AlertDialog
        motionPreset="slideInBottom"
        onClose={onClose1}
        isOpen={isOpen1}
        isCentered
      >
        <AlertDialogOverlay />

        <AlertDialogContent>
          <AlertDialogHeader>Save Changes</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            Are you sure you want to make these changes.
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button
              onClick={() => {
                setBlockData({
                  result: prevBlockData,
                  isLoading: false,
                  error: false,
                });
                setAdjustBtnEdit(false);
                onClose1();
              }}
            >
              No
            </Button>
            <Button colorScheme="telegram" ml={3} onClick={updateBlockPosition}>
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
};

export default BlocksCms;
