import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Card,
  CardBody,
  Flex,
  Heading,
  Link,
  Spinner,
  Text,
  useToast,
} from "@chakra-ui/react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import axios from "axios";
import { useSelector } from "react-redux";

const PlayerTermsAndConditionPage = () => {
  const [termsAndConditionData, setTermsAndConditionData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [isUpdated, setIsUpdated] = useState(false);
  const [discardBtnLoading, setDiscardBtnLoading] = useState(false);
  const [saveBtnLoading, setSaveBtnLoading] = useState(false);

  const Toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getTermsAndConditionData = () => {
    setTermsAndConditionData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/cms-player-termsAndCondition-details`,
      headers: {},
    };

    axios
      .request(config)
      .then((response) => {
        setTermsAndConditionData({
          result: response.data,
          isLoading: false,
          error: false,
        });
        setDiscardBtnLoading(false);
        setIsUpdated(false);
      })
      .catch((error) => {
        console.log(error);
        setTermsAndConditionData({
          result: [],
          isLoading: false,
          error: true,
        });
        setDiscardBtnLoading(false);
        setIsUpdated(false);
        if (error.response.status === 403) {
          Toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          Toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const updateTermsAndConditionData = (id) => {
    let data = JSON.stringify({
      termsAndConditionData: `${termsAndConditionData.result[0].termsAndConditionData}`,
    });

    if (termsAndConditionData.result[0].termsAndConditionData.length === 36) {
      setSaveBtnLoading(false);
      Toast({
        title: "Please add atleast 10 words in about us",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else {
      let config = {
        method: "patch",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/cms/update/cms-player-termsAndCondition-details/${id}`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {
          setSaveBtnLoading(false);
          setIsUpdated(false);
          Toast({
            title: "Registration Details",
            description: "Successfully updated...",
            status: "success",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log(error);
          setSaveBtnLoading(false);
          if (error.response.status === 403) {
            Toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            Toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    }
  };
  const createTermsAndConditionData = () => {
    let data = JSON.stringify({
      termsAndConditionData: `${termsAndConditionData.result[0].termsAndConditionData}`,
    });

    if (termsAndConditionData.result[0].termsAndConditionData.length < 10) {
      setSaveBtnLoading(false);
      Toast({
        title: "Please add at least 10 words in the registration details.",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else {
      let config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/cms/create/cms-player-termsAndCondition-details`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {
          setSaveBtnLoading(false);
          setIsUpdated(false);
          Toast({
            title: "Registration Details",
            description: "Successfully created...",
            status: "success",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
          getTermsAndConditionData(); // Fetch the newly created data to update the state
        })
        .catch((error) => {
          console.log(error);
          setSaveBtnLoading(false);
          if (error.response.status === 403) {
            Toast({
              title: "You don't have access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            Toast({
              title: "Something went wrong, please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    }
  };


  useEffect(() => {
    getTermsAndConditionData();
  }, []);

  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="CMS | About Us" content="container">
        <Flex justifyContent={"space-between"} alignItems={"center"}>
          <Breadcrumb fontWeight="medium" fontSize="sm">
            <BreadcrumbItem>
              <BreadcrumbLink>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>

            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink>CMS</BreadcrumbLink>
            </BreadcrumbItem>

            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink href="#">Player T&C</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
          {userData?.accessScopes?.cms?.includes("write") && (
            <Box>
              <Button
                variant={"outline"}
                colorScheme="red"
                size={"sm"}
                py={5}
                px={4}
                mr={2}
                isDisabled={!isUpdated}
                isLoading={discardBtnLoading}
                onClick={() => {
                  setDiscardBtnLoading(true);
                  getTermsAndConditionData();
                }}
              >
                Discard
              </Button>
              <Button
                variant={"outline"}
                colorScheme="green"
                size={"sm"}
                py={5}
                px={4}
                isDisabled={!isUpdated}
                isLoading={saveBtnLoading}
                onClick={() => {
                  setSaveBtnLoading(true);
                     if (termsAndConditionData.result[0]?._id) {
                       updateTermsAndConditionData(
                         termsAndConditionData.result[0]._id
                       );
                     } else {
                       createTermsAndConditionData();
                     }
                }}
              >
                Save Changes
              </Button>
            </Box>
          )}
        </Flex>
        {termsAndConditionData.isLoading ? (
          <Flex
            w={"full"}
            justifyContent={"center"}
            alignItems={"center"}
            mt={12}
          >
            <Spinner size={"lg"} />
          </Flex>
        ) : !termsAndConditionData.isLoading && !termsAndConditionData.error ? (
          <>
            <Card mt={4}>
              <CardBody>
                <Heading as="h4" size="md" mb={3}>
                Player T&C
                </Heading>
                {userData?.accessScopes?.cms?.includes("write") ? (
                  <ReactQuill
                    theme="snow"
                    value={
                      termsAndConditionData?.result[0]?.termsAndConditionData ||
                      ""
                    }
                    onChange={(e) => {
                      setTermsAndConditionData((prev) => ({
                        ...prev,
                        result: [
                          { ...prev.result[0], termsAndConditionData: e },
                        ],
                      }));
                      setIsUpdated(true);
                    }}
                  />
                ) : (
                  <ReactQuill
                    theme="snow"
                    value={
                      termsAndConditionData?.result[0]?.termsAndConditionData ||
                      ""
                    }
                    readOnly
                    onChange={(e) => {
                      setTermsAndConditionData((prev) => ({
                        ...prev,
                        result: [
                          { ...prev.result[0], termsAndConditionData: e },
                        ],
                      }));
                      setIsUpdated(true);
                    }}
                  />
                )}
              </CardBody>
            </Card>
          </>
        ) : (
          <Flex
            w={"full"}
            justifyContent={"center"}
            alignItems={"center"}
            mt={12}
          >
            <Text color={"red.500"}>
              Something went wrong, please try again later...
            </Text>
          </Flex>
        )}
      </Layout>
    </Box>
  );
};

export default PlayerTermsAndConditionPage;
