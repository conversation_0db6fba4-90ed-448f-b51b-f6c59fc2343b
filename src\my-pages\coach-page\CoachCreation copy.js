import React, { useState } from "react";
import Layout from "../../layout/default";
import {
  Avatar,
  Box,
  Card,
  CardBody,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Select,
  Text,
  Tooltip,
  Button,
  Divider,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Checkbox,
  Textarea,
  Image,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
} from "@chakra-ui/react";
import { FaEdit } from "react-icons/fa";
import { MdDelete } from "react-icons/md";
import { FaChevronDown } from "react-icons/fa";
import ReactQuill from "react-quill";
import { Link, useNavigate } from "react-router-dom";
import { IoMdArrowRoundBack } from "react-icons/io";
import BasicDetailsCoach from "./BasicDetailsCoach";
import ProfessionalDetails from "./ProfessionalDetails";
import KYCDetailsCoach from "./KYCDetailsCoach";

const CoachCreation = () => {
  const [addAddress, setAddAddress] = useState([]);
  const [addCoachingExperience, setCoachingExperience] = useState([]);
  const [addPlayingExperience, setPlayingExperience] = useState([]);
  const [addAward, setAward] = useState([]);
  const [docImagePreview, setDocImagePreview] = useState([]);
  const [profileImagePreview, setProfileImagePreview] = useState(
    "https://media.istockphoto.com/id/1300845620/vector/user-icon-flat-isolated-on-white-background-user-symbol-vector-illustration.jpg?s=612x612&w=0&k=20&c=yBeyba0hUkh14_jgv1OKqIH0CCSWU_4ckRkAoy2p73o="
  );

  const navigate = useNavigate();

  // Function to handle image preview when new images are selected
  const handleImageChange = (e) => {
    const files = e.target.files;
    if (files) {
      const previews = [];
      for (let i = 0; i < files.length; i++) {
        const reader = new FileReader();
        reader.onloadend = () => {
          previews.push(reader.result);
          if (previews.length === files.length) {
            setDocImagePreview([...docImagePreview, ...previews]);
            e.target.value = null;
          }
        };
        reader.readAsDataURL(files[i]);
      }
    }
  };

  // Function to handle removing an image by index
  const handleRemoveImage = (index) => {
    const updatedPreviews = [...docImagePreview];
    updatedPreviews.splice(index, 1);
    setDocImagePreview(updatedPreviews);
  };

  // Profile Image functionality
  // Function to handle image preview when a new image is selected
  const handleProfileImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileImagePreview(reader.result);
        e.target.value = null;
      };
      reader.readAsDataURL(file);
    }
  };

  // Function to handle removing the image
  const handleRemoveProfileImage = () => {
    setProfileImagePreview(null);
  };

  // Function to trigger file input click
  const handleSelectImagesClick = () => {
    document.getElementById("fileInput").click();
  };

  const removeAdd = (id) => {
    setAddAddress((prev) => prev.filter((z) => z.id !== id));
  };
  const removeCoaching = (id) => {
    setCoachingExperience((prev) => prev.filter((z) => z.id !== id));
  };
  const removePlaying = (id) => {
    setPlayingExperience((prev) => prev.filter((z) => z.id !== id));
  };
  const removeAward = (id) => {
    setAward((prev) => prev.filter((z) => z.id !== id));
  };
  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="Courses" content="container">
        {/* Breadcrumb */}
        <Flex justifyContent={"space-between"} alignItems={"center"} mb={6}>
          <Flex justifyContent={"center"} alignItems={"center"}>
            <Tooltip label="Back">
              <Text
                mr={3}
                as={"span"}
                fontSize={"28px"}
                cursor={"pointer"}
                onClick={() => navigate(-1)}
              >
                <IoMdArrowRoundBack />
              </Text>
            </Tooltip>
            <Breadcrumb fontWeight="medium" fontSize="sm">
              <BreadcrumbItem>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbItem>

              <BreadcrumbItem>
                <Link to="/coach-page">Coaches</Link>
              </BreadcrumbItem>

              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Create Coach</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          {/* <Flex>
            <Button variant={"solid"} colorScheme="red" size={"sm"} px={8}>
              Discard
            </Button>
            <Button
              variant={"solid"}
              colorScheme="green"
              size={"sm"}
              px={8}
              ml={4}
            >
              Save Changes
            </Button>
          </Flex> */}
        </Flex>
        {/* --- Tabs --- */}
        <Flex justifyContent={"flex-start"} alignItems={"center"}>
          <Tabs
            variant="soft-rounded"
            colorScheme="telegram"
            size={"md"}
            w={"full"}
          >
            <TabList>
              <Tab>Basic Details</Tab>
              <Tab>Professional Details</Tab>
              <Tab>KYC Details</Tab>
            </TabList>

            <TabPanels>
              <TabPanel>
                <BasicDetailsCoach />
              </TabPanel>
              <TabPanel>
                <ProfessionalDetails />
              </TabPanel>
              <TabPanel>
                <KYCDetailsCoach />
              </TabPanel>
            </TabPanels>
          </Tabs>
        </Flex>
      </Layout>
    </Box>
  );
};

export default CoachCreation;

// ### Coach Address Component ### //
const CoachAddress = ({ id, removeAdd }) => {
  return (
    <Card my={6}>
      <CardBody>
        <Flex justifyContent={"space-between"} alignItems={"center"} mb={4}>
          <Heading as="h4" size="md" mb={0}>
            {"Address " + id}
          </Heading>
          <Button
            colorScheme="red"
            size={"sm"}
            px={4}
            onClick={() => removeAdd(id)}
          >
            Remove
          </Button>
        </Flex>
        <Divider />
        <Flex justifyContent={"space-between"} alignItems={"center"} my={4}>
          <FormControl flexBasis={"48%"}>
            <FormLabel>Facility Name</FormLabel>
            <Input type="text" placeholder="Enter facility name" />
          </FormControl>
          <FormControl flexBasis={"48%"}>
            <FormLabel>Country</FormLabel>
            <Input type="text" placeholder="Enter country name" />
          </FormControl>
        </Flex>
        <Flex justifyContent={"space-between"} alignItems={"center"} my={4}>
          <FormControl flexBasis={"48%"}>
            <FormLabel>Address Line 1</FormLabel>
            <Input type="text" placeholder="Enter address line 1" />
          </FormControl>
          <FormControl flexBasis={"48%"}>
            <FormLabel>Address Line 2</FormLabel>
            <Input type="text" placeholder="Enter address line 2" />
          </FormControl>
        </Flex>
        <Flex justifyContent={"space-between"} alignItems={"center"} my={4}>
          <FormControl flexBasis={"31%"}>
            <FormLabel>City</FormLabel>
            <Input type="text" placeholder="Enter city name" />
          </FormControl>
          <FormControl flexBasis={"31%"}>
            <FormLabel>State</FormLabel>
            <Input type="text" placeholder="Enter state name" />
          </FormControl>
          <FormControl flexBasis={"31%"}>
            <FormLabel>Pincode</FormLabel>
            <Input type="number" placeholder="Enter pincode" />
          </FormControl>
        </Flex>
        <FormControl flexBasis={"48%"}>
          <FormLabel>Ameneties</FormLabel>
          <ReactQuill
            theme="snow"
            value={""}
            // onChange={(e) => {
            //   setPolicyData((prev) => ({
            //     ...prev,
            //     result: [{ ...prev.result[0], TermsAndConditions: e }],
            //   }));
            //   setIsUpdated(true);
            // }}
          />
        </FormControl>
      </CardBody>
    </Card>
  );
};

// ### Professional Details __/\__Blocks__/\__ ### //
const ProfessionalBlocks = ({
  title,
  placeholder,
  id,
  keyword,
  removeCoaching,
  removePlaying,
  removeAward,
}) => {
  const [imagePreview, setImagePreview] = useState(null);

  // Function to handle image preview when a new image is selected
  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
        e.target.value = null;
      };
      reader.readAsDataURL(file);
    }
  };

  // Function to handle removing the image
  const handleRemoveImage = () => {
    setImagePreview(null);
  };
  return (
    <Card>
      <CardBody>
        <FormControl>
          <Flex justifyContent={"space-between"} alignItems={"center"} mb={4}>
            <Heading as="h4" size="md" mb={0}>
              {title}
            </Heading>
            <Button
              colorScheme="red"
              size={"sm"}
              px={4}
              onClick={() => {

                if (keyword === "coaching") {
                  removeCoaching(id);
                } else if (keyword === "playing") {
                  removePlaying(id);
                } else if (keyword === "award") {
                  removeAward(id);
                }
              }}
            >
              Remove
            </Button>
          </Flex>
          <Divider />
          <Flex justifyContent={"space-between"} alignItems={"center"}>
            <Textarea placeholder={placeholder} flexBasis={"60%"} />
            <Flex
              flexDirection="column"
              alignItems="flex-start"
              flexBasis={"33%"}
            >
              {imagePreview && (
                <Image
                  src={imagePreview}
                  alt="Preview"
                  maxW="165px"
                  maxH="165px"
                />
              )}
              {!imagePreview && (
                <>
                  <Text as={"span"} fontWeight={"semibold"}>
                    Add Image<span style={{ color: "red" }}>*</span>
                  </Text>
                  <Input
                    type="file"
                    onChange={handleImageChange}
                    mt={1}
                    cursor={"pointer"}
                  />
                </>
              )}
              {imagePreview && (
                <Button
                  mt={2}
                  colorScheme="red"
                  size={"sm"}
                  onClick={handleRemoveImage}
                >
                  Remove Image
                </Button>
              )}
            </Flex>
          </Flex>
        </FormControl>
      </CardBody>
    </Card>
  );
};
