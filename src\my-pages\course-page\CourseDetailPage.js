import React, { useEffect, useState } from "react";
import CourseUpdate from "./CourseUpdate";
import { useParams } from "react-router-dom";
import axios from "axios";
import { Box, Flex, Spinner, Text, useToast } from "@chakra-ui/react";
import Layout from "../../layout/default";

const CourseDetailPage = () => {
  const [courseData, setCourseData] = useState({
    result: {},
    isLoading: false,
    error: false,
  });
  const [renderOnChange,setRenderOnChange] = useState(0);
  const { id } = useParams();
  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(' ')[1];
  const getCourse = () => {
    setCourseData({ result: {}, isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/course/${id}`,
      headers: {
        Authorization: `Bearer ${token}`
      },
    };

    axios
      .request(config)
      .then((response) => {
        setCourseData({
          result: response.data,
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        setCourseData({ result: {}, isLoading: false, error: error });
        if (error.response?.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  useEffect(() => {
    getCourse();
  }, [id,renderOnChange]);

  const renderMe = () => {
    setRenderOnChange((prev) => prev + 1);
  }

  return (
    <>
      {courseData.isLoading ? (
        <Box bgColor={"#f2f2f2"}>
          <Layout title="Course Details" content="container">
            <Flex justifyContent={"center"} alignItems={"center"} my={16}>
              <Spinner size="lg" />
            </Flex>
          </Layout>
        </Box>
      ) : courseData.error ? (
        <Box bgColor={"#f2f2f2"}>
          <Layout title="Courses" content="container">
            <Flex justifyContent={"center"} alignItems={"center"} my={16}>
              <Text color={"red.500"} size={"md"}>
                Something went wrong, please try again later
              </Text>
            </Flex>
          </Layout>
        </Box>
      ) : (
        <CourseUpdate courseData={courseData.result} renderMe={renderMe} />
      )}
    </>
  );
};

export default CourseDetailPage;
