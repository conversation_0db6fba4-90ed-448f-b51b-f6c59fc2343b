import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";

import {
  Bread<PERSON><PERSON>bLink,
  Breadcrumb,
  BreadcrumbItem,
  Flex,
  Box,
  Card,
  CardBody,
  Button,
  FormControl,
  Input,
  FormLabel,
  Heading,
  useToast,
  FormErrorMessage,
  Image,
  Tooltip,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
  Text,
} from "@chakra-ui/react";
import { Link } from "react-router-dom";
import ReactQuill from "react-quill";
import axios from "axios";
import { Spinner } from "react-bootstrap";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useSelector } from "react-redux";

const RegistrationInstructor = () => {
  const [renderMe, setRenderMe] = useState(0);
  const [instructorData, setInstructorData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getRegistrationInstruction = () => {
    setInstructorData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/registration-instructor`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setInstructorData({
          result: response.data[0],
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        setInstructorData({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const renderOnDiscard = () => {
    setRenderMe((prev) => prev + 1);
  };

  useEffect(() => {
    getRegistrationInstruction();
  }, [renderMe]);

  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="CMS | Registration" content="container">
        <Flex justifyContent={"space-between"} alignItems={"center"}>
          <Breadcrumb fontWeight="medium" fontSize="sm">
            <BreadcrumbItem>
              <BreadcrumbLink>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>

            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink>CMS</BreadcrumbLink>
            </BreadcrumbItem>

            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink href="#">Instructor Registration</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
        </Flex>
        {instructorData?.isLoading && !instructorData?.error ? (
          <Flex justifyContent={"center"} alignItems={"center"} my={16}>
            <Spinner size="xl" />
          </Flex>
        ) : instructorData?.error ? (
          <Flex justifyContent={"center"} alignItems={"center"} my={16}>
            <Text color={"red.500"} fontWeight={"semibold"}>
              Something went wrong please try again later
            </Text>
          </Flex>
        ) : (
          <InstructorData
            data={instructorData?.result}
            renderOnDiscard={renderOnDiscard}
          />
        )}
      </Layout>
    </Box>
  );
};

export default RegistrationInstructor;

const InstructorData = ({ data, renderOnDiscard }) => {
  const [isBtnLoading, setIsBtnLoading] = useState(false);
  const [previewImage, setPreviewImage] = useState(data?.image || "");
  const toast = useToast();
  const id = data._id;
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const [isOpen1, setIsOpen1] = useState(false);
  const onClose1 = () => setIsOpen1(false);
  const onOpen1 = () => setIsOpen1(true);

  const validationSchema = Yup.object().shape({
    description: Yup.string().test(
      "html-length",
      "Description must be at least 10 characters",
      (value) => {
        const plainText = value.replace(/<[^>]+>/g, "");
        return plainText.length >= 10;
      }
    ),
    buttonUrl: Yup.string().required("Button URL is required"),
  });

  const formik = useFormik({
    initialValues: {
      description: data?.description,
      image: data?.image,
      buttonUrl: data?.buttonUrl,
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      setIsBtnLoading(true);
      let data = JSON.stringify(values);
      let config = {
        method: "patch",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/cms/update/registration-instructor/${id}`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {

          setIsBtnLoading(false);
          onClose1();
          toast({
            title: "Instructor registration data is updated",
            status: "success",
            duration: 3500,
            position: "top",
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log(error);
          setIsBtnLoading(false);
          onClose1();
          if (error.response.status === 403) {
            toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    },
  });

  // Function to handle image selection
  const handleImageChange = async (e) => {
    const file = e.target.files[0];
    const reader = new FileReader();

    reader.readAsDataURL(file);
    reader.onloadend = () => {
      setPreviewImage(reader.result);
    };

    const formData = new FormData();
    formData.append("image", file);
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/cms/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const url = response.data.url;
      formik.setFieldValue("image", url);
      setPreviewImage(url);
      toast({
        title: "Image Uploaded",
        status: "success",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    } catch (error) {
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const removeImage = async (url) => {
    try {
      const formData = new FormData();
      formData.append("url", url);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/cms/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const resp = response?.data;
      if (resp) {
        if (url) {
          toast({
            title: "Image deleted",
            status: "success",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
          formik.setFieldValue("image", undefined);
          setPreviewImage("");
        } else {
          toast({
            title: "Something went wrong while deleting image.",
            status: "error",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
        }
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  return (
    <>
      <form onSubmit={formik.handleSubmit}>
        <Card mt={4}>
          <CardBody>
            <FormControl
              isInvalid={
                formik.errors.description && formik.touched.description
              }
            >
              <FormLabel>Description</FormLabel>
              {userData?.accessScopes?.cms?.includes("write") ? (
                <ReactQuill
                  id="description"
                  name="description"
                  value={formik.values.description}
                  onChange={(content) =>
                    formik.setFieldValue("description", content)
                  }
                  theme="snow"
                />
              ) : (
                <ReactQuill
                  id="description"
                  name="description"
                  readOnly
                  value={formik.values.description}
                  onChange={(content) =>
                    formik.setFieldValue("description", content)
                  }
                  theme="snow"
                />
              )}
              {formik.errors.description && formik.touched.description && (
                <FormErrorMessage>{formik.errors.description}</FormErrorMessage>
              )}
            </FormControl>
          </CardBody>
        </Card>

        <Card mt={4}>
          <CardBody>
            <FormControl>
              <FormLabel>Image</FormLabel>
              {userData?.accessScopes?.cms?.includes("write") && (
                <Input
                  type="file"
                  id="imageFile"
                  name="imageFile"
                  accept="image/*"
                  onChange={handleImageChange}
                />
              )}
              {previewImage && (
                <Box mt={2}>
                  <Image
                    src={previewImage}
                    alt="Preview"
                    boxSize="150px"
                    objectFit="cover"
                  />
                  {userData?.accessScopes?.cms?.includes("delete") && (
                    <Button
                      mt={2}
                      size="sm"
                      colorScheme="red"
                      onClick={() => removeImage(previewImage)}
                      px={12}
                    >
                      Remove
                    </Button>
                  )}
                </Box>
              )}
            </FormControl>
          </CardBody>
        </Card>

        <Card mt={4}>
          <CardBody>
            <FormControl
              isInvalid={formik.errors.buttonUrl && formik.touched.buttonUrl}
            >
              <FormLabel>Registration Link</FormLabel>
              {userData?.accessScopes?.cms?.includes("write") ? (
                <Input
                  type="text"
                  placeholder="Enter registration button link URL"
                  id="buttonUrl"
                  name="buttonUrl"
                  onChange={formik.handleChange}
                  value={formik.values.buttonUrl}
                />
              ) : (
                <Input
                  type="text"
                  isReadOnly
                  placeholder="Enter registration button link URL"
                  id="buttonUrl"
                  name="buttonUrl"
                  onChange={formik.handleChange}
                  value={formik.values.buttonUrl}
                />
              )}
              {formik.errors.buttonUrl && formik.touched.buttonUrl && (
                <FormErrorMessage>{formik.errors.buttonUrl}</FormErrorMessage>
              )}
            </FormControl>
          </CardBody>
        </Card>
        {userData?.accessScopes?.cms?.includes("write") && (
          <Flex justifyContent="space-between" alignItems="center" mt={6}>
            <Button
              colorScheme="red"
              size="sm"
              flexBasis="49%"
              onClick={() => renderOnDiscard()}
            >
              Discard
            </Button>
            {!previewImage ? (
              <Tooltip label={"Please select image"} bg={"red.600"} hasArrow>
                <Button
                  colorScheme="green"
                  size="sm"
                  flexBasis="49%"
                  type="submit"
                  isDisabled={!previewImage}
                >
                  Save Changes
                </Button>
              </Tooltip>
            ) : (
              <Button
                colorScheme="green"
                size="sm"
                flexBasis="49%"
                onClick={formik.handleSubmit}
              >
                Save Changes
              </Button>
            )}
          </Flex>
        )}

        {/* Save Changes Alert */}
        {/* <AlertDialog
          motionPreset="slideInBottom"
          onClose={onClose1}
          isOpen={isOpen1}
          isCentered
        >
          <AlertDialogOverlay />

          <AlertDialogContent>
            <AlertDialogHeader>Save Changes</AlertDialogHeader>
            <AlertDialogCloseButton />
            <AlertDialogBody>
              Are you sure you want to make these changes.
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button onClick={() => {
              onClose1()
              renderOnDiscard();
              }}>No</Button>
              <Button
                colorScheme="telegram"
                ml={3}
                type="submit"
                isLoading={isBtnLoading}
                onClick={formik.handleSubmit}
              >
                Yes
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog> */}
      </form>
    </>
  );
};
