import React from "react";
import { Box, Button, Center, Flex, Heading, Text } from "@chakra-ui/react";
import { useNavigate } from "react-router-dom";

const VerificationPending = () => {
  const navigate = useNavigate();

  const handleLogout = async () => {
    const token = sessionStorage.getItem("admintoken");
    try {
      const myHeaders = new Headers();
      if (token) {
        myHeaders.append("Cookie", `accessToken=${token}`);
      }
      const requestOptions = {
        method: "DELETE",
        headers: myHeaders,
        credentials: "include",
        redirect: "follow",
      };
      await fetch(
        `${process.env.REACT_APP_BASE_URL}/api/academy/logout`,
        requestOptions
      );
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      localStorage.clear();
      navigate("/login");
    }
  };

  return (
    <Flex
      minH="100vh"
      direction="column"
      justify="center"
      align="center"
      bg="gray.100"
    >
      <Center flex={1} flexDirection="column">
        <Heading size="lg" mb={4} color="teal.600">
          Academy Verification Under Review
        </Heading>
        <Text fontSize="lg" color="gray.700" mb={6}>
          Thank you for registering! Your details are under review. You will be
          notified once your account is verified.
        </Text>
        <Button colorScheme="teal" onClick={handleLogout} alignSelf="center">
          Logout
        </Button>
      </Center>
    </Flex>
  );
};

export default VerificationPending;
