import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Card,
  CardBody,
  Flex,
  Heading,
  Link,
  Spinner,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  useToast,
} from "@chakra-ui/react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import axios from "axios";
import { getCookie } from "../../utilities/auth";
import { useSelector } from "react-redux";

const PolicyCms = () => {
  const [policyData, setPolicyData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [isUpdated, setIsUpdated] = useState(false);
  const [discardBtnLoading, setDiscardBtnLoading] = useState(false);
  const [saveBtnLoading, setSaveBtnLoading] = useState(false);

  const Toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getPolicy = () => {
    setPolicyData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/cms/policy`,
      headers: {},
    };

    axios
      .request(config)
      .then((response) => {
        setPolicyData({
          result: response.data,
          isLoading: false,
          error: false,
        });
        setDiscardBtnLoading(false);
        setIsUpdated(false);
      })
      .catch((error) => {
        console.log(error);
        setPolicyData({ result: [], isLoading: false, error: true });
        setDiscardBtnLoading(false);
        setIsUpdated(false);
        if (error.response.status === 403) {
          Toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          Toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const updatePolicy = (id) => {
    let data = JSON.stringify({
      TermsAndConditions: `${policyData.result[0].TermsAndConditions}`,
      privacyPolicy: `${policyData.result[0].privacyPolicy}`,
      customerGrievancePolicy: `${policyData.result[0].customerGrievancePolicy}`,
    });

    if (!(policyData.result[0].TermsAndConditions.length >= 17)) {
      setSaveBtnLoading(false);
      Toast({
        title: "Please add atleast 10 words in terms and condition",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else if (!(policyData.result[0].privacyPolicy.length >= 17)) {
      setSaveBtnLoading(false);
      Toast({
        title: "Please add atleast 10 words in privacy policy",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else if (!(policyData.result[0].customerGrievancePolicy.length >= 17)) {
      setSaveBtnLoading(false);
      Toast({
        title: "Please add atleast 10 words in customer grievance policy",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else {
      let config = {
        method: "patch",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/cms/update/policy/${id}`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {
  
          setSaveBtnLoading(false);
          setIsUpdated(false);
          Toast({
            title: "Terms of services",
            description: "Successfully updated...",
            status: "success",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log(error);
          setSaveBtnLoading(false);
          if (error.response.status === 403) {
            Toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            Toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    }
  };

  useEffect(() => {
    getPolicy();
  }, []);

  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="CMS | Help" content="container">
        <Flex justifyContent={"space-between"} alignItems={"center"}>
          <Breadcrumb fontWeight="medium" fontSize="sm">
            <BreadcrumbItem>
              <BreadcrumbLink>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>

            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink>CMS</BreadcrumbLink>
            </BreadcrumbItem>

            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink href="#">Help</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
          {userData?.accessScopes?.cms?.includes("write") && (
            <Box>
              <Button
                variant={"outline"}
                colorScheme="red"
                size={"sm"}
                py={5}
                px={4}
                mr={2}
                isDisabled={!isUpdated}
                isLoading={discardBtnLoading}
                onClick={() => {
                  setDiscardBtnLoading(true);
                  getPolicy();
                }}
              >
                Discard
              </Button>
              <Button
                variant={"outline"}
                colorScheme="green"
                size={"sm"}
                py={5}
                px={4}
                isDisabled={!isUpdated}
                isLoading={saveBtnLoading}
                onClick={() => {
                  setSaveBtnLoading(true);
                  updatePolicy(policyData.result[0]._id);
                }}
              >
                Save Changes
              </Button>
            </Box>
          )}
        </Flex>
        {policyData.isLoading ? (
          <Flex
            w={"full"}
            justifyContent={"center"}
            alignItems={"center"}
            mt={12}
          >
            <Spinner size={"lg"} />
          </Flex>
        ) : !policyData.isLoading && !policyData.error ? (
          <>
            {/* <Card mt={4}>
              <CardBody>
                <Heading as="h4" size="md" mb={3}>
                  Terms & Conditions
                </Heading>
                {userData?.accessScopes?.cms?.includes("write") ? (
                  <ReactQuill
                    theme="snow"
                    value={policyData?.result[0]?.TermsAndConditions || ""}
                    onChange={(e) => {
                      setPolicyData((prev) => ({
                        ...prev,
                        result: [{ ...prev.result[0], TermsAndConditions: e }],
                      }));
                      setIsUpdated(true);
                    }}
                  />
                ) : (
                  <ReactQuill
                    theme="snow"
                    value={policyData?.result[0]?.TermsAndConditions || ""}
                    readOnly
                    onChange={(e) => {
                      setPolicyData((prev) => ({
                        ...prev,
                        result: [{ ...prev.result[0], TermsAndConditions: e }],
                      }));
                      setIsUpdated(true);
                    }}
                  />
                )}
              </CardBody>
            </Card>

            <Card mt={4}>
              <CardBody>
                <Heading as="h4" size="md" mb={3}>
                  Privacy Policy
                </Heading>
                {userData?.accessScopes?.cms?.includes("write") ? (
                  <ReactQuill
                    theme="snow"
                    value={policyData?.result[0]?.privacyPolicy || ""}
                    onChange={(e) => {
                      setPolicyData((prev) => ({
                        ...prev,
                        result: [{ ...prev.result[0], privacyPolicy: e }],
                      }));
                      setIsUpdated(true);
                    }}
                  />
                ) : (
                  <ReactQuill
                    theme="snow"
                    value={policyData?.result[0]?.privacyPolicy || ""}
                    readOnly
                    onChange={(e) => {
                      setPolicyData((prev) => ({
                        ...prev,
                        result: [{ ...prev.result[0], privacyPolicy: e }],
                      }));
                      setIsUpdated(true);
                    }}
                  />
                )}
              </CardBody>
            </Card>

            <Card mt={4}>
              <CardBody>
                <Heading as="h4" size="md" mb={3}>
                  Customer Grievance Policy
                </Heading>
                {userData?.accessScopes?.cms?.includes("write") ? (
                  <ReactQuill
                    theme="snow"
                    value={policyData?.result[0]?.customerGrievancePolicy || ""}
                    onChange={(e) => {
                      setPolicyData((prev) => ({
                        ...prev,
                        result: [
                          { ...prev.result[0], customerGrievancePolicy: e },
                        ],
                      }));
                      setIsUpdated(true);
                    }}
                  />
                ) : (
                  <ReactQuill
                    theme="snow"
                    readOnly
                    value={policyData?.result[0]?.customerGrievancePolicy || ""}
                    onChange={(e) => {
                      setPolicyData((prev) => ({
                        ...prev,
                        result: [
                          { ...prev.result[0], customerGrievancePolicy: e },
                        ],
                      }));
                      setIsUpdated(true);
                    }}
                  />
                )}
              </CardBody>
            </Card> */}
            <Tabs>
              <TabList>
                <Tab>Terms of services</Tab>
                <Tab>Privacy Policy</Tab>
                <Tab>Customer Grievance Policy</Tab>
              </TabList>

              <TabPanels>
                <TabPanel>
                  <Card mt={4}>
                    <CardBody>
                      <Heading as="h4" size="md" mb={3}>
                        Terms of services
                      </Heading>
                      {userData?.accessScopes?.cms?.includes("write") ? (
                        <ReactQuill
                          theme="snow"
                          value={
                            policyData?.result[0]?.TermsAndConditions || ""
                          }
                          onChange={(e) => {
                            setPolicyData((prev) => ({
                              ...prev,
                              result: [
                                { ...prev.result[0], TermsAndConditions: e },
                              ],
                            }));
                            setIsUpdated(true);
                          }}
                        />
                      ) : (
                        <ReactQuill
                          theme="snow"
                          value={
                            policyData?.result[0]?.TermsAndConditions || ""
                          }
                          readOnly
                          onChange={(e) => {
                            setPolicyData((prev) => ({
                              ...prev,
                              result: [
                                { ...prev.result[0], TermsAndConditions: e },
                              ],
                            }));
                            setIsUpdated(true);
                          }}
                        />
                      )}
                    </CardBody>
                  </Card>
                </TabPanel>
                <TabPanel>
                  <Card mt={4}>
                    <CardBody>
                      <Heading as="h4" size="md" mb={3}>
                        Privacy Policy
                      </Heading>
                      {userData?.accessScopes?.cms?.includes("write") ? (
                        <ReactQuill
                          theme="snow"
                          value={policyData?.result[0]?.privacyPolicy || ""}
                          onChange={(e) => {
                            setPolicyData((prev) => ({
                              ...prev,
                              result: [{ ...prev.result[0], privacyPolicy: e }],
                            }));
                            setIsUpdated(true);
                          }}
                        />
                      ) : (
                        <ReactQuill
                          theme="snow"
                          value={policyData?.result[0]?.privacyPolicy || ""}
                          readOnly
                          onChange={(e) => {
                            setPolicyData((prev) => ({
                              ...prev,
                              result: [{ ...prev.result[0], privacyPolicy: e }],
                            }));
                            setIsUpdated(true);
                          }}
                        />
                      )}
                    </CardBody>
                  </Card>
                </TabPanel>
                <TabPanel>
                  <Card mt={4}>
                    <CardBody>
                      <Heading as="h4" size="md" mb={3}>
                        Customer Grievance Policy
                      </Heading>
                      {userData?.accessScopes?.cms?.includes("write") ? (
                        <ReactQuill
                          theme="snow"
                          value={
                            policyData?.result[0]?.customerGrievancePolicy || ""
                          }
                          onChange={(e) => {
                            setPolicyData((prev) => ({
                              ...prev,
                              result: [
                                {
                                  ...prev.result[0],
                                  customerGrievancePolicy: e,
                                },
                              ],
                            }));
                            setIsUpdated(true);
                          }}
                        />
                      ) : (
                        <ReactQuill
                          theme="snow"
                          readOnly
                          value={
                            policyData?.result[0]?.customerGrievancePolicy || ""
                          }
                          onChange={(e) => {
                            setPolicyData((prev) => ({
                              ...prev,
                              result: [
                                {
                                  ...prev.result[0],
                                  customerGrievancePolicy: e,
                                },
                              ],
                            }));
                            setIsUpdated(true);
                          }}
                        />
                      )}
                    </CardBody>
                  </Card>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </>
        ) : (
          <Flex
            w={"full"}
            justifyContent={"center"}
            alignItems={"center"}
            mt={12}
          >
            <Text color={"red.500"}>
              Something went wrong, please try again later...
            </Text>
          </Flex>
        )}
      </Layout>
    </Box>
  );
};

export default PolicyCms;
