import React, { useState } from "react";
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Heading,
  useToast,
  Image,
} from "@chakra-ui/react";
import LogoImage from "../../assets/images/mask/MainKhelCoach.svg";
import axios from "axios";

const ForgotPassword = () => {
  const [email, setEmail] = useState("");
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const handleReset = async () => {
    if (!email) {
      toast({ title: "Please enter your email", status: "warning" });
      return;
    }
    setIsLoading(true);
    try {
      await axios.post(`${process.env.REACT_APP_BASE_URL}/api/academy/requestResetPassword/${encodeURIComponent(email)}`);
      toast({ title: "Reset link sent! Please check your email.", status: "success" });
    } catch (err) {
      const apiError = err?.response?.data;
      let errorMsg = apiError?.message || "Failed to send reset link";
      if (apiError?.errors && Array.isArray(apiError.errors) && apiError.errors.length > 0) {
        errorMsg = apiError.errors[0].message || errorMsg;
      }
      toast({ title: errorMsg, status: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Flex
      flexDirection="column"
      width="100wh"
      height="100vh"
      backgroundColor="gray.200"
      justifyContent="center"
      alignItems="center"
    >
      <Stack
        flexDir="column"
        mb="2"
        justifyContent="center"
        alignItems="center"
      >
        <Image src={LogoImage} alt="logo" w={"200px"} mb={4} />
        <Box minW={{ base: "90%", md: "468px" }}>
          {/* <Heading mb={6} textAlign="center">Forgot Password</Heading> */}
          <Stack
            spacing={4}
            p="1rem"
            backgroundColor="whiteAlpha.900"
            boxShadow="md"
          >
            <FormControl>
              <FormLabel>Email address</FormLabel>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
              />
            </FormControl>
            <Button colorScheme="teal" w="full" onClick={handleReset} isLoading={isLoading} disabled={isLoading}>
              Send Reset Link
            </Button>
          </Stack>
        </Box>
      </Stack>
    </Flex>
  );
};

export default ForgotPassword;