 // Function to get a cookie by name
 export const getCookie = (name) => {
    const cookies = document.cookie.split(";");
    for (let cookie of cookies) {
      const [cookieName, cookieValue] = cookie.split("=");
      if (cookieName.trim() === name) {
        return cookieValue;
      }
    }
    return null;
  };

// Function to set a session cookie (without expiration)
export const setCookie = (name, value) => {
    document.cookie = `${name}=${value}; path=/`;
  };

// Function to delete a cookie by name
export const deleteCookie = (name) => {
    document.cookie = `${name}=; path=/;`;
  };